<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Ensure the configuration_charge table exists and has the correct structure
 *
 * @return bool True if the table exists with the correct structure, false otherwise
 */
function ensureConfigurationChargeTable() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureConfigurationChargeTable");
        return false;
    }

    // Check if the table exists
    $tableExists = $conn->query("SHOW TABLES LIKE 'configuration_charge'");

    if (!$tableExists) {
        error_log("Error checking if configuration_charge table exists: " . $conn->error);
        $conn->close();
        return false;
    }

    // Check if the table exists
    $exists = $tableExists->num_rows > 0;

    if (!$exists) {
        error_log("configuration_charge table does not exist, creating it now");

        // Create the table
        $createTableSQL = "CREATE TABLE `configuration_charge` (
            `id_config` int(11) NOT NULL AUTO_INCREMENT,
            `annee_universitaire` varchar(9) COLLATE utf8mb4_general_ci NOT NULL,
            `charge_minimale` int(11) NOT NULL,
            `departement_id` int(11) DEFAULT NULL,
            `type_enseignant` enum('enseignant','vacataire') COLLATE utf8mb4_general_ci DEFAULT 'enseignant',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id_config`),
            UNIQUE KEY `unique_annee_dept_type` (`annee_universitaire`, `departement_id`, `type_enseignant`),
            KEY `fk_config_charge_departement` (`departement_id`),
            CONSTRAINT `fk_config_charge_departement` FOREIGN KEY (`departement_id`) REFERENCES `departement` (`id_departement`) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $result = $conn->query($createTableSQL);

        if (!$result) {
            error_log("Error creating configuration_charge table: " . $conn->error);
            $conn->close();
            return false;
        }

        error_log("configuration_charge table created successfully");
        $conn->close();
        return true;
    } else {
        error_log("configuration_charge table exists");
        $conn->close();
        return true;
    }
}

/**
 * Get all workload configurations (global configurations)
 *
 * @param int $departmentId Department ID (not used - configurations are global)
 * @return array Array of workload configurations or error
 */
function getWorkloadConfigurationsByDepartment($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    // Get all global configurations, ordered by academic year and role
    $sql = "SELECT * FROM configuration_charge ORDER BY annee_universitaire DESC, role";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getWorkloadConfigurationsByDepartment: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching workload configurations: " . $error];
    }

    $configurations = [];
    while ($row = mysqli_fetch_assoc($result)) {
        // Ensure role field is properly set
        if (empty($row['role'])) {
            $row['role'] = 'enseignant'; // Default value for existing records
        }
        $configurations[] = $row;
    }

    mysqli_close($conn);
    return $configurations;
}

/**
 * Create a new workload configuration
 *
 * @param array $data Configuration data
 * @return array Success message or error
 */
function createWorkloadConfiguration($data) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (empty($data['annee_universitaire']) || empty($data['charge_minimale']) || empty($data['role'])) {
        mysqli_close($conn);
        return ["error" => "Missing required fields: annee_universitaire, charge_minimale, role"];
    }

    // Sanitize inputs
    $anneeUniversitaire = mysqli_real_escape_string($conn, $data['annee_universitaire']);
    $chargeMinimale = intval($data['charge_minimale']);
    $role = mysqli_real_escape_string($conn, $data['role']);

    // Validate role
    if (!in_array($role, ['enseignant', 'vacataire'])) {
        mysqli_close($conn);
        return ["error" => "Invalid role. Must be 'enseignant' or 'vacataire'"];
    }

    // Check if configuration already exists for this academic year and role combination
    $checkSql = "SELECT id_config FROM configuration_charge
                 WHERE annee_universitaire = '$anneeUniversitaire' AND role = '$role'";

    $checkResult = mysqli_query($conn, $checkSql);

    if (mysqli_num_rows($checkResult) > 0) {
        mysqli_close($conn);
        return ["error" => "Configuration already exists for this academic year and role"];
    }

    $sql = "INSERT INTO configuration_charge (annee_universitaire, role, charge_minimale)
            VALUES ('$anneeUniversitaire', '$role', '$chargeMinimale')";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in createWorkloadConfiguration: " . $error);
        mysqli_close($conn);
        return ["error" => "Error creating workload configuration: " . $error];
    }

    $configId = mysqli_insert_id($conn);
    mysqli_close($conn);

    return ["success" => "Workload configuration created successfully", "id" => $configId];
}

/**
 * Update an existing workload configuration
 *
 * @param int $configId Configuration ID
 * @param array $data Updated configuration data
 * @return array Success message or error
 */
function updateWorkloadConfiguration($configId, $data) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (empty($data['annee_universitaire']) || empty($data['charge_minimale']) || empty($data['role'])) {
        mysqli_close($conn);
        return ["error" => "Missing required fields: annee_universitaire, charge_minimale, role"];
    }

    // Sanitize inputs
    $configId = intval($configId);
    $anneeUniversitaire = mysqli_real_escape_string($conn, $data['annee_universitaire']);
    $chargeMinimale = intval($data['charge_minimale']);
    $role = mysqli_real_escape_string($conn, $data['role']);

    // Validate role
    if (!in_array($role, ['enseignant', 'vacataire'])) {
        mysqli_close($conn);
        return ["error" => "Invalid role. Must be 'enseignant' or 'vacataire'"];
    }

    // Check if another configuration exists for this academic year and role combination (excluding current record)
    $checkSql = "SELECT id_config FROM configuration_charge
                 WHERE annee_universitaire = '$anneeUniversitaire' AND role = '$role' AND id_config != '$configId'";

    $checkResult = mysqli_query($conn, $checkSql);

    if (mysqli_num_rows($checkResult) > 0) {
        mysqli_close($conn);
        return ["error" => "Another configuration already exists for this academic year and role"];
    }

    $sql = "UPDATE configuration_charge
            SET annee_universitaire = '$anneeUniversitaire',
                role = '$role',
                charge_minimale = '$chargeMinimale'
            WHERE id_config = '$configId'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in updateWorkloadConfiguration: " . $error);
        mysqli_close($conn);
        return ["error" => "Error updating workload configuration: " . $error];
    }

    if (mysqli_affected_rows($conn) === 0) {
        mysqli_close($conn);
        return ["error" => "Configuration not found or no changes made"];
    }

    mysqli_close($conn);
    return ["success" => "Workload configuration updated successfully"];
}

/**
 * Delete a workload configuration
 *
 * @param int $configId Configuration ID
 * @return array Success message or error
 */
function deleteWorkloadConfiguration($configId) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $configId = intval($configId);

    $sql = "DELETE FROM configuration_charge WHERE id_config = '$configId'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteWorkloadConfiguration: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting workload configuration: " . $error];
    }

    if (mysqli_affected_rows($conn) === 0) {
        mysqli_close($conn);
        return ["error" => "Configuration not found"];
    }

    mysqli_close($conn);
    return ["success" => "Workload configuration deleted successfully"];
}

/**
 * Get current academic year
 *
 * @return string Current academic year in format YYYY-YYYY
 */
function getCurrentAcademicYear() {
    $currentYear = date('Y');
    $currentMonth = date('n');

    // Academic year starts in September (month 9)
    if ($currentMonth >= 9) {
        return $currentYear . '-' . ($currentYear + 1);
    } else {
        return ($currentYear - 1) . '-' . $currentYear;
    }
}

/**
 * Get workload configuration by ID
 *
 * @param int $configId Configuration ID
 * @return array Configuration data or error
 */
function getWorkloadConfigurationById($configId) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $configId = intval($configId);

    $sql = "SELECT * FROM configuration_charge WHERE id_config = '$configId'";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getWorkloadConfigurationById: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching workload configuration: " . $error];
    }

    if (mysqli_num_rows($result) === 0) {
        mysqli_close($conn);
        return ["error" => "Configuration not found"];
    }

    $configuration = mysqli_fetch_assoc($result);

    // Ensure role field is properly set for existing records
    if (empty($configuration['role'])) {
        $configuration['role'] = 'enseignant'; // Default value for existing records
    }

    mysqli_close($conn);

    return $configuration;
}
?>
