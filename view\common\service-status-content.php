<?php
/**
 * Contenu de la page d'état des services - Pour inclusion dans d'autres pages
 */

// Récupérer les données du service depuis les variables globales ou paramètres
global $serviceStatusData;

if (isset($serviceStatusData)) {
    $serviceKey = $serviceStatusData['serviceKey'];
    $serviceName = $serviceStatusData['serviceName'];
    $reason = $serviceStatusData['reason'];
    $additionalInfo = $serviceStatusData['additionalInfo'];
    $userRole = $serviceStatusData['userRole'];
} else {
    // Fallback vers les paramètres GET si pas de données globales
    $serviceKey = $_GET['service'] ?? 'unknown';
    $serviceName = $_GET['name'] ?? 'Service';
    $reason = $_GET['reason'] ?? 'temporarily_unavailable';
    $userRole = $_SESSION['user_role'] ?? 'user';
    $additionalInfo = [];
}

// Configuration des messages selon la raison
$statusConfig = [
    'temporarily_unavailable' => [
        'icon' => 'bi-clock-history',
        'color' => 'warning',
        'title' => 'Service Temporairement Indisponible',
        'description' => 'Ce service n\'est pas disponible pour le moment.'
    ],
    'maintenance' => [
        'icon' => 'bi-tools',
        'color' => 'info',
        'title' => 'Maintenance en Cours',
        'description' => 'Ce service est actuellement en maintenance.'
    ],
    'period_closed' => [
        'icon' => 'bi-calendar-x',
        'color' => 'warning',
        'title' => 'Période Fermée',
        'description' => 'La période d\'accès à ce service est actuellement fermée.'
    ],
    'not_activated' => [
        'icon' => 'bi-pause-circle',
        'color' => 'secondary',
        'title' => 'Service Non Activé',
        'description' => 'Ce service n\'a pas encore été activé par l\'administration.'
    ]
];

$config = $statusConfig[$reason] ?? $statusConfig['temporarily_unavailable'];
?>

<style>
.service-status-container {
    max-width: 600px;
    margin: 2rem auto;
    padding: 2rem;
}

.status-card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.status-header {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.status-icon {
    font-size: 3.5rem;
    margin-bottom: 1rem;
}

.status-body {
    padding: 2rem;
}

.info-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin: 1.5rem 0;
}

.contact-info {
    background-color: #e7f3ff;
    border-left: 3px solid #007bff;
    padding: 0.75rem;
    margin: 1rem 0;
    border-radius: 4px;
}

.action-buttons {
    text-align: center;
    margin-top: 2rem;
}

.refresh-button {
    background: #007bff;
    border: none;
    border-radius: 6px;
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 500;
    transition: all 0.3s ease;
}

.refresh-button:hover {
    background: #0056b3;
    color: white;
}
</style>

<div class="service-status-container">
    <div class="card status-card">
        <!-- En-tête du statut -->
        <div class="status-header">
            <div class="status-icon text-<?php echo $config['color']; ?>">
                <i class="<?php echo $config['icon']; ?>"></i>
            </div>
            <h2 class="mb-2"><?php echo $config['title']; ?></h2>
            <h4 class="text-muted mb-0"><?php echo htmlspecialchars($serviceName); ?></h4>
        </div>
        
        <!-- Corps du message -->
        <div class="status-body">
            <div class="text-center mb-4">
                <p class="lead"><?php echo $config['description']; ?></p>
            </div>
            
            <!-- Informations contextuelles -->
            <div class="info-section">
                <?php if ($reason === 'period_closed'): ?>
                    <p class="mb-2"><i class="bi bi-info-circle text-primary"></i> <strong>La période de soumission est actuellement fermée.</strong></p>
                    <p class="mb-0">Contactez votre chef de département pour plus d'informations.</p>
                <?php elseif ($reason === 'maintenance'): ?>
                    <p class="mb-2"><i class="bi bi-tools text-info"></i> <strong>Le service est temporairement en maintenance.</strong></p>
                    <p class="mb-0">Réessayez dans quelques minutes.</p>
                <?php elseif ($reason === 'not_activated'): ?>
                    <p class="mb-2"><i class="bi bi-pause-circle text-secondary"></i> <strong>Le service n'a pas encore été activé.</strong></p>
                    <p class="mb-0">Contactez votre chef de département si nécessaire.</p>
                <?php else: ?>
                    <p class="mb-2"><i class="bi bi-exclamation-circle text-warning"></i> <strong>Le service est temporairement indisponible.</strong></p>
                    <p class="mb-0">Réessayez dans quelques minutes.</p>
                <?php endif; ?>
            </div>
            
            <!-- Informations de contact -->
            <div class="contact-info">
                <p class="mb-0"><i class="bi bi-telephone text-primary"></i> <strong>Besoin d'aide ?</strong> Contactez votre chef de département.</p>
            </div>
            
            <!-- Boutons d'action -->
            <div class="action-buttons">
                <button onclick="location.reload()" class="btn refresh-button me-3">
                    <i class="bi bi-arrow-clockwise"></i> Actualiser
                </button>
                
                <?php if ($userRole === 'enseignant'): ?>
                    <a href="<?php echo BASE_URL; ?>/view/enseignant/dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Tableau de Bord
                    </a>
                <?php elseif ($userRole === 'etudiant'): ?>
                    <a href="<?php echo BASE_URL; ?>/view/etudiant/dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Tableau de Bord
                    </a>
                <?php elseif ($userRole === 'chef de departement'): ?>
                    <a href="<?php echo BASE_URL; ?>/view/chef/dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Tableau de Bord
                    </a>
                <?php elseif ($userRole === 'coordinateur'): ?>
                    <a href="<?php echo BASE_URL; ?>/view/coordinator/dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Tableau de Bord
                    </a>
                <?php else: ?>
                    <a href="<?php echo BASE_URL; ?>/view/dashboard.php" class="btn btn-outline-primary">
                        <i class="bi bi-house"></i> Tableau de Bord
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh toutes les 30 secondes pour vérifier si le service est redevenu disponible
setInterval(function() {
    // Vérifier discrètement le statut du service
    fetch('<?php echo BASE_URL; ?>/route/serviceManagementRoute.php?action=status&service_key=<?php echo urlencode($serviceKey); ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.is_active) {
                // Service redevenu actif, recharger la page
                location.reload();
            }
        })
        .catch(error => {
            console.log('Vérification du statut du service:', error);
        });
}, 30000);
</script>
