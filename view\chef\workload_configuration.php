<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Get the department head's ID and department ID from the session
$chefId = $_SESSION['user']['teacher_id'] ?? null;
$departmentId = $_SESSION['user']['department_id'] ?? null;

// Get department head's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$chefName = trim($prenom . ' ' . $nom);
if (empty($chefName)) {
    $chefName = $_SESSION['user']['username'] ?? 'Chef de département';
}

// Get department name from session
$departmentName = $_SESSION['user']['department_name'] ?? 'Non spécifié';

// Page title
$pageTitle = "Configuration de la Charge de Travail";
$currentPage = "workload_configuration.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --light-bg: #f8f9fa;
            --border-color: #dee2e6;
            --text-muted: #6c757d;
            --pastel-blue: #e3f2fd;
            --pastel-green: #e8f5e8;
            --pastel-orange: #fff3e0;
            --pastel-purple: #f3e5f5;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: var(--primary-color);
        }

        .main-content {
            margin-left: 250px;
            min-height: 100vh;
            background-color: var(--light-bg);
        }

        .page-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
            font-size: 1.75rem;
        }

        .department-header {
            background: linear-gradient(135deg, var(--pastel-blue), var(--pastel-purple));
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
            border-radius: 8px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }

        .btn-danger {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            font-weight: 500;
        }

        .table-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid var(--border-color);
        }

        .table {
            margin-bottom: 0;
        }

        .table th {
            background-color: var(--pastel-blue);
            color: var(--primary-color);
            font-weight: 600;
            border: none;
            padding: 1rem 0.75rem;
            font-size: 0.9rem;
        }

        .table td {
            padding: 0.75rem;
            vertical-align: middle;
            border-color: var(--border-color);
        }

        .table tbody tr:hover {
            background-color: var(--pastel-green);
            transition: background-color 0.2s ease;
        }

        .modal-content {
            border-radius: 12px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--pastel-blue), var(--pastel-purple));
            border-radius: 12px 12px 0 0;
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
        }

        .modal-title {
            color: var(--primary-color);
            font-weight: 600;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 0.75rem;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .form-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .badge {
            font-size: 0.8rem;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
        }

        .badge.bg-success {
            background-color: var(--success-color) !important;
        }

        .badge.bg-primary {
            background-color: var(--secondary-color) !important;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: var(--border-color);
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1060;
            min-width: 300px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
            }

            .table-responsive {
                border-radius: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="d-flex">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <h1 class="page-title">
                    <i class="fas fa-cogs me-2"></i>
                    Configuration de la Charge de Travail
                </h1>

                <!-- Department Header -->
                <div class="department-header">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="mb-1">
                                <i class="fas fa-building me-2"></i>
                                Département: <?php echo htmlspecialchars($departmentName); ?>
                            </h5>
                            <p class="mb-0 text-muted">
                                <i class="fas fa-user me-2"></i>
                                Chef de département: <?php echo htmlspecialchars($chefName); ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#configModal">
                                <i class="fas fa-plus me-2"></i>
                                Nouvelle Configuration
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Configurations Table -->
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Configurations Existantes
                        </h5>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="loadConfigurations()">
                            <i class="fas fa-sync-alt me-1"></i>
                            Actualiser
                        </button>
                    </div>

                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2 text-muted">Chargement des configurations...</p>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="configurationsTable">
                            <thead>
                                <tr>
                                    <th>Année Universitaire</th>
                                    <th>Charge Minimale (heures)</th>
                                    <th>Date de Création</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="configurationsTableBody">
                                <!-- Data will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <div class="empty-state" id="emptyState" style="display: none;">
                        <i class="fas fa-inbox"></i>
                        <h5>Aucune configuration trouvée</h5>
                        <p>Commencez par créer une nouvelle configuration de charge de travail.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1" aria-labelledby="configModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="configModalLabel">
                        <i class="fas fa-cogs me-2"></i>
                        Nouvelle Configuration
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <input type="hidden" id="configId" name="id_config">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="anneeUniversitaire" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>
                                        Année Universitaire *
                                    </label>
                                    <input type="text" class="form-control" id="anneeUniversitaire" name="annee_universitaire"
                                           placeholder="2024-2025" pattern="^\d{4}-\d{4}$" required>
                                    <div class="form-text">Format: YYYY-YYYY (ex: 2024-2025)</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Portée
                                    </label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-info">Configuration Globale</span>
                                        <div class="form-text">Cette configuration s'applique à tous les enseignants</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="chargeMinimale" class="form-label">
                                        <i class="fas fa-clock me-1"></i>
                                        Charge Minimale (heures par année) *
                                    </label>
                                    <input type="number" class="form-control" id="chargeMinimale" name="charge_minimale"
                                           min="1" max="1000" required>
                                    <div class="form-text">Nombre d'heures minimum que doit enseigner un professeur par année académique</div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Information:</strong> Cette configuration s'appliquera à tous les enseignants du type sélectionné dans votre département pour l'année universitaire spécifiée.
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveConfiguration()">
                        <i class="fas fa-save me-1"></i>
                        <span id="saveButtonText">Enregistrer</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Confirmer la Suppression
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer cette configuration de charge de travail ?</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-warning me-2"></i>
                        Cette action est irréversible.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Annuler
                    </button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash me-1"></i>
                        Supprimer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentConfigId = null;
        let deleteConfigId = null;
        const departmentId = <?php echo json_encode($departmentId); ?>;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadConfigurations();
            setCurrentAcademicYear();
        });

        // Load configurations from server
        function loadConfigurations() {
            showLoading(true);

            fetch(`../../controller/configurationChargeController.php?action=getWorkloadConfigurationsByDepartment&department_id=${departmentId}`)
                .then(response => {
                    // Check if response is ok
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    // Check if response is JSON
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        return response.text().then(text => {
                            console.error('Non-JSON response:', text);
                            throw new Error('Server returned non-JSON response');
                        });
                    }

                    return response.json();
                })
                .then(data => {
                    showLoading(false);

                    if (data.error) {
                        showNotification('Erreur lors du chargement des configurations: ' + data.error, 'error');
                        return;
                    }

                    displayConfigurations(data.data);
                })
                .catch(error => {
                    showLoading(false);
                    console.error('Error loading configurations:', error);
                    showNotification('Erreur de connexion au serveur: ' + error.message, 'error');
                });
        }

        // Display configurations in table
        function displayConfigurations(configurations) {
            const tbody = document.getElementById('configurationsTableBody');
            const emptyState = document.getElementById('emptyState');
            const table = document.getElementById('configurationsTable');

            if (!configurations || configurations.length === 0) {
                table.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            table.style.display = 'table';
            emptyState.style.display = 'none';

            tbody.innerHTML = configurations.map(config => `
                <tr>
                    <td>
                        <span class="badge bg-primary">${config.annee_universitaire}</span>
                    </td>
                    <td>
                        <strong>${config.charge_minimale}</strong> heures
                    </td>
                    <td>
                        ${formatDate(config.created_at)}
                    </td>
                    <td>
                        <button type="button" class="btn btn-warning btn-sm me-1" onclick="editConfiguration(${config.id_config})" title="Modifier">
                            <i class="fas fa-edit"></i>
                            Modifier
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteConfiguration(${config.id_config})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                            Supprimer
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Set current academic year as default
        function setCurrentAcademicYear() {
            fetch('../../controller/configurationChargeController.php?action=getCurrentAcademicYear')
                .then(response => response.json())
                .then(data => {
                    if (data.data && data.data.academic_year) {
                        document.getElementById('anneeUniversitaire').value = data.data.academic_year;
                    }
                })
                .catch(error => {
                    console.error('Error getting current academic year:', error);
                });
        }

        // Show/hide loading spinner
        function showLoading(show) {
            const spinner = document.getElementById('loadingSpinner');
            const table = document.getElementById('configurationsTable');

            if (show) {
                spinner.style.display = 'block';
                table.style.display = 'none';
            } else {
                spinner.style.display = 'none';
            }
        }

        // Save configuration (create or update)
        function saveConfiguration() {
            const form = document.getElementById('configForm');
            const formData = new FormData(form);

            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // Prepare data
            const data = {
                annee_universitaire: formData.get('annee_universitaire'),
                charge_minimale: parseInt(formData.get('charge_minimale'))
            };

            // Add ID for update
            const configId = formData.get('id_config');
            if (configId) {
                data.id_config = parseInt(configId);
            }

            // Determine action
            const action = configId ? 'updateWorkloadConfiguration' : 'createWorkloadConfiguration';

            // Send request
            fetch(`../../controller/configurationChargeController.php?action=${action}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification('Erreur: ' + data.error, 'error');
                    return;
                }

                showNotification(data.message || 'Configuration enregistrée avec succès', 'success');

                // Close modal and reload data
                const modal = bootstrap.Modal.getInstance(document.getElementById('configModal'));
                modal.hide();
                resetForm();
                loadConfigurations();
            })
            .catch(error => {
                console.error('Error saving configuration:', error);
                showNotification('Erreur de connexion au serveur', 'error');
            });
        }

        // Edit configuration
        function editConfiguration(configId) {
            fetch(`../../controller/configurationChargeController.php?action=getWorkloadConfigurationById&config_id=${configId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        showNotification('Erreur: ' + data.error, 'error');
                        return;
                    }

                    const config = data.data;

                    // Fill form with configuration data
                    document.getElementById('configId').value = config.id_config;
                    document.getElementById('anneeUniversitaire').value = config.annee_universitaire;
                    document.getElementById('chargeMinimale').value = config.charge_minimale;

                    // Update modal title and button text
                    document.getElementById('configModalLabel').innerHTML = '<i class="fas fa-edit me-2"></i>Modifier Configuration';
                    document.getElementById('saveButtonText').textContent = 'Mettre à jour';

                    // Show modal
                    const modal = new bootstrap.Modal(document.getElementById('configModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error loading configuration:', error);
                    showNotification('Erreur de connexion au serveur', 'error');
                });
        }

        // Delete configuration
        function deleteConfiguration(configId) {
            deleteConfigId = configId;
            const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
            modal.show();
        }

        // Confirm delete
        function confirmDelete() {
            if (!deleteConfigId) return;

            fetch('../../controller/configurationChargeController.php?action=deleteWorkloadConfiguration', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id_config: deleteConfigId })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showNotification('Erreur: ' + data.error, 'error');
                    return;
                }

                showNotification(data.message || 'Configuration supprimée avec succès', 'success');

                // Close modal and reload data
                const modal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                modal.hide();
                deleteConfigId = null;
                loadConfigurations();
            })
            .catch(error => {
                console.error('Error deleting configuration:', error);
                showNotification('Erreur de connexion au serveur', 'error');
            });
        }

        // Reset form
        function resetForm() {
            document.getElementById('configForm').reset();
            document.getElementById('configId').value = '';
            document.getElementById('configModalLabel').innerHTML = '<i class="fas fa-cogs me-2"></i>Nouvelle Configuration';
            document.getElementById('saveButtonText').textContent = 'Enregistrer';
            setCurrentAcademicYear();
        }

        // Reset form when modal is hidden
        document.getElementById('configModal').addEventListener('hidden.bs.modal', function () {
            resetForm();
        });

        // Format date for display
        function formatDate(dateString) {
            if (!dateString) return '-';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>
