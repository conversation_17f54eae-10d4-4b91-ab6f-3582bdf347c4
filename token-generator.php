<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Inclure les fichiers nécessaires
require_once BASE_PATH . "/config/db.php";

// Fonction pour générer un token
function generateToken() {
    try {
        // Connexion à la base de données
        $conn = getConnection();

        if (!$conn) {
            return ['success' => false, 'error' => 'Erreur de connexion à la base de données'];
        }

        // Vérifier si la table users existe
        $result = mysqli_query($conn, "SHOW TABLES LIKE 'users'");

        if (mysqli_num_rows($result) == 0) {
            // Créer la table users
            $sql = "CREATE TABLE users (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'enseignant', 'chef_de_departement', 'coordinateur', 'vacataire', 'etudiant') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            if (!mysqli_query($conn, $sql)) {
                mysqli_close($conn);
                return ['success' => false, 'error' => 'Erreur lors de la création de la table users: ' . mysqli_error($conn)];
            }

            echo "<p>Table users créée avec succès</p>";
        }

        // Vérifier si la table password_reset_tokens existe
        $result = mysqli_query($conn, "SHOW TABLES LIKE 'password_reset_tokens'");

        if (mysqli_num_rows($result) == 0) {
            // Créer la table password_reset_tokens
            $sql = "CREATE TABLE password_reset_tokens (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) NOT NULL,
                token VARCHAR(100) NOT NULL,
                expiry DATETIME NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            if (!mysqli_query($conn, $sql)) {
                mysqli_close($conn);
                return ['success' => false, 'error' => 'Erreur lors de la création de la table password_reset_tokens: ' . mysqli_error($conn)];
            }

            echo "<p>Table password_reset_tokens créée avec succès</p>";
        }

        // Nom d'utilisateur pour le test
        $username = 'test_user';

        // Vérifier si l'utilisateur existe
        $sql = "SELECT * FROM users WHERE username = '$username'";
        $result = mysqli_query($conn, $sql);

        if (mysqli_num_rows($result) == 0) {
            // Créer l'utilisateur
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $sql = "INSERT INTO users (username, password, role) VALUES ('$username', '$hashedPassword', 'etudiant')";

            if (!mysqli_query($conn, $sql)) {
                mysqli_close($conn);
                return ['success' => false, 'error' => 'Erreur lors de la création de l\'utilisateur: ' . mysqli_error($conn)];
            }

            echo "<p>Utilisateur $username créé avec succès</p>";
        } else {
            echo "<p>L'utilisateur $username existe déjà</p>";
        }

        // Générer un token
        $token = bin2hex(random_bytes(32));
        $expiry = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Supprimer les anciens tokens pour cet utilisateur
        $sql = "DELETE FROM password_reset_tokens WHERE username = '$username'";
        mysqli_query($conn, $sql);

        // Insérer le nouveau token
        $sql = "INSERT INTO password_reset_tokens (username, token, expiry) VALUES ('$username', '$token', '$expiry')";

        if (!mysqli_query($conn, $sql)) {
            mysqli_close($conn);
            return ['success' => false, 'error' => 'Erreur lors de l\'insertion du token: ' . mysqli_error($conn)];
        }

        mysqli_close($conn);

        // Construire le lien
        $host = $_SERVER['HTTP_HOST'];

        // Récupérer le chemin de base dynamiquement
        require_once BASE_PATH . "/config/constants.php";
        $link = "http://$host" . BASE_URL . "/view/initialize-password.php?token=$token";

        return [
            'success' => true,
            'username' => $username,
            'token' => $token,
            'expiry' => $expiry,
            'link' => $link
        ];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

// Générer le token
$result = generateToken();

// Afficher le résultat
echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Générateur de Token</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #1a73e8;
        }
        .result {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .success {
            color: #28a745;
        }
        .error {
            color: #dc3545;
        }
        .link {
            word-break: break-all;
            margin: 20px 0;
        }
        button {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #1557b0;
        }
    </style>
</head>
<body>
    <h1>Générateur de Token pour l'initialisation de mot de passe</h1>";

if ($result['success']) {
    echo "<div class='result'>
        <h2 class='success'>Token généré avec succès</h2>
        <p><strong>Nom d'utilisateur:</strong> " . htmlspecialchars($result['username']) . "</p>
        <p><strong>Token:</strong> " . htmlspecialchars($result['token']) . "</p>
        <p><strong>Date d'expiration:</strong> " . htmlspecialchars($result['expiry']) . "</p>
        <p><strong>Lien d'initialisation:</strong></p>
        <div class='link'><a href='" . htmlspecialchars($result['link']) . "' target='_blank'>" . htmlspecialchars($result['link']) . "</a></div>
        <button onclick=\"window.open('" . htmlspecialchars($result['link']) . "', '_blank')\">Ouvrir la page d'initialisation</button>
    </div>";
} else {
    echo "<div class='result'>
        <h2 class='error'>Erreur lors de la génération du token</h2>
        <p>" . htmlspecialchars($result['error']) . "</p>
    </div>";
}

echo "</body>
</html>";
?>
