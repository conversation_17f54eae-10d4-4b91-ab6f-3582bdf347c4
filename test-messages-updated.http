###############################################
# Tests pour le modèle messagesModel.php
###############################################

# Variables globales
@baseUrl = http://localhost/Projet-Web
@messagesRoute = {{baseUrl}}/route/messagesRoute.php

###############################################
# Tests pour les messages
###############################################

### 1. Récupérer tous les messages (avec pagination)
GET {{messagesRoute}}?action=getAll&page=1&perPage=10
Content-Type: application/json

### 2. Créer un nouveau message (sans destinataire)
POST {{messagesRoute}}?action=create
Content-Type: application/json

{
  "sender_id": 10,
  "title": "Message de test",
  "content": "Ceci est un message de test sans destinataire"
}

### 3. Créer un nouveau message (avec destinataire)
POST {{messagesRoute}}?action=create
Content-Type: application/json

{
  "sender_id": 10,
  "title": "Message avec destinataire",
  "content": "Ceci est un message de test avec un destinataire",
  "receiver_id": 2
}

### 4. Créer un nouveau message (avec tous les paramètres)
POST {{messagesRoute}}?action=create
Content-Type: application/json

{
  "sender_id": 10,
  "title": "Message complet",
  "content": "Ceci est un message de test avec tous les paramètres",
  "receiver_id": 2,
  "media_url": "https://example.com/media/image.jpg",
  "file_path": "/uploads/documents/file.pdf"
}

### 5. Créer un message administratif
POST {{messagesRoute}}?action=createAdminMessage
Content-Type: application/json

{
  "sender_id": 10,
  "title": "Message administratif",
  "content": "Ceci est un message administratif de test",
  "receiver_id": 11
}

### 6. Marquer un message comme lu
POST {{messagesRoute}}?action=markAsRead
Content-Type: application/json

{
  "id": 1
}

### 7. Marquer tous les messages comme lus
POST {{messagesRoute}}?action=markAllAsRead
Content-Type: application/json

### 8. Supprimer un message
POST {{messagesRoute}}?action=deleteMessage
Content-Type: application/json

{
  "id": 1
}

### 9. Récupérer le nombre de messages non lus
GET {{messagesRoute}}?action=getUnreadCount
Content-Type: application/json

### 10. Vérifier si la base de données a besoin d'être mise à jour
GET {{messagesRoute}}?action=checkColumns
Content-Type: application/json
