<?php
/**
 * Utilitaires pour la gestion des pages d'état des services
 */

require_once __DIR__ . '/../config/constants.php';

/**
 * Affiche la page d'état d'un service avec l'interface complète
 */
function showServiceStatusPage($serviceKey, $serviceName, $reason = 'temporarily_unavailable', $additionalInfo = []) {
    // Construire l'URL avec les paramètres
    $params = [
        'service' => $serviceKey,
        'name' => $serviceName,
        'reason' => $reason
    ];

    // Ajouter des informations supplémentaires si fournies
    if (!empty($additionalInfo)) {
        $params = array_merge($params, $additionalInfo);
    }

    $queryString = http_build_query($params);
    $statusPageUrl = BASE_URL . "/view/common/service-status.php?" . $queryString;

    // Rediriger vers la page d'état
    header("Location: " . $statusPageUrl);
    exit();
}

/**
 * Inclut directement le contenu de la page d'état dans la page actuelle
 */
function includeServiceStatusContent($serviceKey, $serviceName, $reason = 'temporarily_unavailable', $additionalInfo = []) {
    // Définir les variables globales pour la page d'état
    global $serviceStatusData;
    $serviceStatusData = [
        'serviceKey' => $serviceKey,
        'serviceName' => $serviceName,
        'reason' => $reason,
        'additionalInfo' => $additionalInfo,
        'userRole' => $_SESSION['user_role'] ?? 'user'
    ];

    // Inclure le contenu de la page d'état
    include __DIR__ . '/../view/common/service-status-content.php';
    exit();
}

/**
 * Génère le contenu de la page d'état directement (pour inclusion)
 */
function renderServiceStatusContent($serviceKey, $serviceName, $reason = 'temporarily_unavailable', $additionalInfo = []) {
    $userRole = $_SESSION['user_role'] ?? 'user';

    // Configuration des messages selon la raison
    $statusConfig = [
        'temporarily_unavailable' => [
            'icon' => 'bi-clock-history',
            'color' => 'warning',
            'title' => 'Service Temporairement Indisponible',
            'description' => 'Ce service n\'est pas disponible pour le moment.',
            'suggestions' => [
                'Réessayez dans quelques minutes',
                'Contactez le support si le problème persiste',
                'Vos données sont en sécurité',
                'Consultez les annonces pour les mises à jour'
            ]
        ],
        'maintenance' => [
            'icon' => 'bi-tools',
            'color' => 'info',
            'title' => 'Maintenance en Cours',
            'description' => 'Ce service est actuellement en maintenance pour améliorer votre expérience.',
            'suggestions' => [
                'Le service est temporairement en maintenance',
                'Aucune action n\'est requise de votre part',
                'Réessayez dans quelques minutes',
                'Les données existantes sont préservées'
            ]
        ],
        'period_closed' => [
            'icon' => 'bi-calendar-x',
            'color' => 'warning',
            'title' => 'Période Fermée',
            'description' => 'La période d\'accès à ce service est actuellement fermée.',
            'suggestions' => [
                'La période de soumission est actuellement fermée',
                'Contactez votre chef de département pour plus d\'informations',
                'Vérifiez régulièrement ou attendez l\'annonce de réouverture',
                'Préparez vos données en attendant la prochaine période'
            ]
        ],
        'not_activated' => [
            'icon' => 'bi-pause-circle',
            'color' => 'secondary',
            'title' => 'Service Non Activé',
            'description' => 'Ce service n\'a pas encore été activé par l\'administration.',
            'suggestions' => [
                'Le service n\'a pas encore été activé par l\'administration',
                'Contactez votre chef de département si nécessaire',
                'Vous serez notifié lors de l\'activation',
                'Consultez les annonces pour les mises à jour'
            ]
        ],
        'access_denied' => [
            'icon' => 'bi-shield-x',
            'color' => 'danger',
            'title' => 'Accès Non Autorisé',
            'description' => 'Vous n\'avez pas les permissions nécessaires pour accéder à ce service.',
            'suggestions' => [
                'Vérifiez que vous êtes connecté avec le bon compte',
                'Contactez votre chef de département pour les permissions',
                'Assurez-vous d\'être dans la bonne période d\'accès',
                'Consultez la documentation utilisateur'
            ]
        ]
    ];

    $config = $statusConfig[$reason] ?? $statusConfig['temporarily_unavailable'];

    // Ajouter des informations supplémentaires si fournies
    if (isset($additionalInfo['last_deactivated'])) {
        $config['last_deactivated'] = $additionalInfo['last_deactivated'];
    }
    if (isset($additionalInfo['next_activation'])) {
        $config['next_activation'] = $additionalInfo['next_activation'];
    }

    return [
        'config' => $config,
        'serviceKey' => $serviceKey,
        'serviceName' => $serviceName,
        'userRole' => $userRole,
        'additionalInfo' => $additionalInfo
    ];
}

/**
 * Vérifie l'état d'un service et redirige si nécessaire
 */
function checkServiceAccessAndRedirect($serviceKey, $serviceName, $requiredRole = null) {
    // Vérifier si le service est actif
    require_once __DIR__ . '/../model/serviceManagementModel.php';

    $isActive = isServiceActive($serviceKey);

    if (!$isActive) {
        $service = getServiceByKey($serviceKey);
        $reason = 'not_activated';

        if ($service) {
            if ($service['last_deactivated_at']) {
                $reason = 'period_closed';
            }
        }

        showServiceStatusPage($serviceKey, $serviceName, $reason, [
            'last_deactivated' => $service['last_deactivated_at'] ?? null,
            'activation_count' => $service['activation_count'] ?? 0
        ]);
    }

    // Vérifier les permissions de rôle si spécifiées
    if ($requiredRole && isset($_SESSION['user_role'])) {
        $userRole = $_SESSION['user_role'];
        $allowedRoles = is_array($requiredRole) ? $requiredRole : [$requiredRole];

        if (!in_array($userRole, $allowedRoles)) {
            showServiceStatusPage($serviceKey, $serviceName, 'access_denied', [
                'required_role' => $requiredRole,
                'user_role' => $userRole
            ]);
        }
    }

    return true; // Service accessible
}

/**
 * Génère les liens de retour selon le rôle utilisateur
 */
function getDashboardUrlByRole($userRole) {
    $dashboardUrls = [
        'enseignant' => BASE_URL . '/view/enseignant/dashboard.php',
        'etudiant' => BASE_URL . '/view/etudiant/dashboard.php',
        'chef de departement' => BASE_URL . '/view/chef_departement/dashboard.php',
        'coordinateur' => BASE_URL . '/view/coordinateur/dashboard.php',
        'admin' => BASE_URL . '/view/admin/dashboard.php',
        'vacataire' => BASE_URL . '/view/enseignant/dashboard.php'
    ];

    return $dashboardUrls[$userRole] ?? BASE_URL . '/view/dashboard.php';
}

/**
 * Génère un message personnalisé selon le service et le rôle
 */
function getServiceSpecificMessage($serviceKey, $userRole) {
    $messages = [
        'ue_preferences' => [
            'enseignant' => 'La période de soumission des préférences d\'enseignement est fermée.',
            'chef de departement' => 'La gestion des préférences UE n\'est pas disponible actuellement.',
            'coordinateur' => 'La période de collecte des préférences UE est fermée.'
        ],
        'grade_submission' => [
            'enseignant' => 'La période de saisie des notes est fermée.',
            'coordinateur' => 'La gestion des notes n\'est pas disponible actuellement.'
        ],
        'course_evaluation' => [
            'etudiant' => 'La période d\'évaluation des cours est fermée.',
            'enseignant' => 'La consultation des évaluations n\'est pas disponible.'
        ],
        'schedule_modification' => [
            'chef de departement' => 'La modification des emplois du temps est fermée.',
            'coordinateur' => 'La gestion des plannings n\'est pas disponible.'
        ]
    ];

    return $messages[$serviceKey][$userRole] ?? 'Ce service n\'est pas disponible actuellement.';
}



/**
 * Génère le JavaScript pour la vérification automatique du statut
 */
function getServiceStatusCheckScript($serviceKey) {
    return "
    <script>
        // Auto-refresh toutes les 30 secondes pour vérifier si le service est redevenu disponible
        setInterval(function() {
            fetch('" . BASE_URL . "/route/serviceManagementRoute.php?action=status&service_key=" . urlencode($serviceKey) . "')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_active) {
                        // Service redevenu actif, rediriger vers la page originale
                        const originalUrl = sessionStorage.getItem('originalServiceUrl');
                        if (originalUrl) {
                            window.location.href = originalUrl;
                        } else {
                            location.reload();
                        }
                    }
                })
                .catch(error => {
                    console.log('Vérification du statut du service:', error);
                });
        }, 30000);

        // Sauvegarder l'URL originale pour redirection automatique
        if (document.referrer && !sessionStorage.getItem('originalServiceUrl')) {
            sessionStorage.setItem('originalServiceUrl', document.referrer);
        }
    </script>";
}

?>
