<?php
/**
 * Service Management Routes
 * Handles routing for service management API endpoints
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../controller/serviceManagementController.php';
require_once __DIR__ . '/../utils/response.php';

$method = $_SERVER['REQUEST_METHOD'];
$serviceKey = isset($_GET['service_key']) ? $_GET['service_key'] : null;
$action = isset($_GET['action']) ? $_GET['action'] : null;

try {
    switch ($method) {
        case 'GET':
            if ($action === 'status' && $serviceKey) {
                checkServiceStatusAPI($serviceKey);
            } elseif ($action === 'access' && $serviceKey) {
                getServiceAccessStatusAPI($serviceKey);
            } elseif ($action === 'logs') {
                getServiceLogsAPI($serviceKey);
            } elseif ($action === 'check_expired') {
                checkExpiredServicesAPI();
            } elseif ($serviceKey) {
                getServiceByKeyAPI($serviceKey);
            } else {
                getAllServicesAPI();
            }
            break;

        case 'POST':
            if ($action === 'activate') {
                activateServiceAPI();
            } elseif ($action === 'deactivate') {
                deactivateServiceAPI();
            } elseif ($action === 'extend') {
                extendServiceAPI();
            } else {
                jsonResponse(['error' => 'Action non spécifiée'], 400);
            }
            break;

        case 'PUT':
            if ($action === 'activate') {
                activateServiceAPI();
            } elseif ($action === 'deactivate') {
                deactivateServiceAPI();
            } elseif ($action === 'extend') {
                extendServiceAPI();
            } else {
                jsonResponse(['error' => 'Action non spécifiée'], 400);
            }
            break;

        default:
            jsonResponse(['error' => 'Méthode non autorisée'], 405);
            break;
    }
} catch (Exception $e) {
    error_log('Error in serviceManagementRoute: ' . $e->getMessage());
    jsonResponse(['error' => 'Erreur serveur interne'], 500);
}

?>
