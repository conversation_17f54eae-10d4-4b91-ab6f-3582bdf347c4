<?php
/**
 * Service Access Middleware
 * Checks if services are active before allowing access to pages
 */

require_once __DIR__ . '/../model/serviceManagementModel.php';

/**
 * Check if UE preferences service is active and redirect if not
 */
function requireUEPreferencesAccess($redirectUrl = null) {
    $accessStatus = checkUEPreferencesServiceAccess();
    
    if (!$accessStatus['allowed']) {
        // Store the access message in session for display
        session_start();
        $_SESSION['service_access_message'] = $accessStatus['message'];
        $_SESSION['service_access_type'] = 'warning';
        
        // Redirect to appropriate page
        if ($redirectUrl) {
            header("Location: $redirectUrl");
        } else {
            // Default redirect based on user role
            if (isset($_SESSION['user_role'])) {
                switch ($_SESSION['user_role']) {
                    case 'enseignant':
                        header("Location: " . BASE_URL . "/view/enseignant/dashboard.php");
                        break;
                    case 'chef de departement':
                        header("Location: " . BASE_URL . "/view/chef_departement/dashboard.php");
                        break;
                    default:
                        header("Location: " . BASE_URL . "/view/auth/login.php");
                        break;
                }
            } else {
                header("Location: " . BASE_URL . "/view/auth/login.php");
            }
        }
        exit();
    }
    
    return $accessStatus;
}

/**
 * Check UE preferences service access without redirecting
 */
function checkUEPreferencesServiceAccess() {
    $isActive = isServiceActive('ue_preferences');
    
    if (!$isActive) {
        $service = getServiceByKey('ue_preferences');
        $message = "La période de soumission des préférences UE est actuellement fermée.";
        
        if ($service && $service['last_deactivated_at']) {
            $lastDeactivated = new DateTime($service['last_deactivated_at']);
            $message .= " Dernière fermeture: " . $lastDeactivated->format('d/m/Y à H:i');
        }
        
        $message .= " Veuillez contacter votre chef de département pour plus d'informations.";
        
        return [
            'allowed' => false,
            'message' => $message,
            'service' => $service
        ];
    }
    
    $service = getServiceByKey('ue_preferences');
    $message = "La période de soumission des préférences UE est active.";
    
    if ($service && $service['end_time']) {
        $endTime = new DateTime($service['end_time']);
        $now = new DateTime();
        
        if ($endTime > $now) {
            $interval = $now->diff($endTime);
            
            if ($interval->days > 0) {
                $message .= " Temps restant: " . $interval->days . " jour(s) " . $interval->h . " heure(s)";
            } elseif ($interval->h > 0) {
                $message .= " Temps restant: " . $interval->h . " heure(s) " . $interval->i . " minute(s)";
            } else {
                $message .= " Temps restant: " . $interval->i . " minute(s)";
            }
            
            $message .= " (jusqu'au " . $endTime->format('d/m/Y à H:i') . ")";
        }
    }
    
    return [
        'allowed' => true,
        'message' => $message,
        'service' => $service
    ];
}

/**
 * Check if grade submission service is active
 */
function requireGradeSubmissionAccess($redirectUrl = null) {
    $isActive = isServiceActive('grade_submission');
    
    if (!$isActive) {
        session_start();
        $_SESSION['service_access_message'] = "La période de soumission des notes est actuellement fermée. Veuillez contacter l'administration pour plus d'informations.";
        $_SESSION['service_access_type'] = 'warning';
        
        if ($redirectUrl) {
            header("Location: $redirectUrl");
        } else {
            header("Location: " . BASE_URL . "/view/enseignant/dashboard.php");
        }
        exit();
    }
    
    return true;
}

/**
 * Check if course evaluation service is active
 */
function requireCourseEvaluationAccess($redirectUrl = null) {
    $isActive = isServiceActive('course_evaluation');
    
    if (!$isActive) {
        session_start();
        $_SESSION['service_access_message'] = "La période d'évaluation des cours est actuellement fermée. Veuillez contacter l'administration pour plus d'informations.";
        $_SESSION['service_access_type'] = 'warning';
        
        if ($redirectUrl) {
            header("Location: $redirectUrl");
        } else {
            header("Location: " . BASE_URL . "/view/etudiant/dashboard.php");
        }
        exit();
    }
    
    return true;
}

/**
 * Check if schedule modification service is active
 */
function requireScheduleModificationAccess($redirectUrl = null) {
    $isActive = isServiceActive('schedule_modification');
    
    if (!$isActive) {
        session_start();
        $_SESSION['service_access_message'] = "La période de modification d'emploi du temps est actuellement fermée. Veuillez contacter l'administration pour plus d'informations.";
        $_SESSION['service_access_type'] = 'warning';
        
        if ($redirectUrl) {
            header("Location: $redirectUrl");
        } else {
            if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'chef de departement') {
                header("Location: " . BASE_URL . "/view/chef_departement/dashboard.php");
            } else {
                header("Location: " . BASE_URL . "/view/enseignant/dashboard.php");
            }
        }
        exit();
    }
    
    return true;
}

/**
 * Display service access message if exists in session
 */
function displayServiceAccessMessage() {
    session_start();
    
    if (isset($_SESSION['service_access_message'])) {
        $message = $_SESSION['service_access_message'];
        $type = isset($_SESSION['service_access_type']) ? $_SESSION['service_access_type'] : 'info';
        
        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>";
        echo "<i class='bi bi-info-circle'></i> " . htmlspecialchars($message);
        echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
        echo "</div>";
        
        // Clear the message after displaying
        unset($_SESSION['service_access_message']);
        unset($_SESSION['service_access_type']);
    }
}

/**
 * Get service status for display in UI
 */
function getServiceStatusForUI($serviceKey) {
    $service = getServiceByKey($serviceKey);
    
    if (!$service) {
        return [
            'exists' => false,
            'active' => false,
            'message' => 'Service non configuré'
        ];
    }
    
    $isActive = isServiceActive($serviceKey);
    
    $status = [
        'exists' => true,
        'active' => $isActive,
        'service' => $service
    ];
    
    if ($isActive && $service['end_time']) {
        $endTime = new DateTime($service['end_time']);
        $now = new DateTime();
        
        if ($endTime > $now) {
            $interval = $now->diff($endTime);
            $status['remaining_time'] = [
                'days' => $interval->days,
                'hours' => $interval->h,
                'minutes' => $interval->i,
                'formatted' => formatTimeRemaining($interval)
            ];
            $status['end_time'] = $endTime->format('d/m/Y à H:i');
        }
    }
    
    return $status;
}

/**
 * Format time remaining for display
 */
function formatTimeRemaining($interval) {
    if ($interval->days > 0) {
        return $interval->days . " jour(s) " . $interval->h . " heure(s)";
    } elseif ($interval->h > 0) {
        return $interval->h . " heure(s) " . $interval->i . " minute(s)";
    } else {
        return $interval->i . " minute(s)";
    }
}

/**
 * Create a service status widget for UI
 */
function renderServiceStatusWidget($serviceKey, $serviceName) {
    $status = getServiceStatusForUI($serviceKey);
    
    if (!$status['exists']) {
        return "<div class='alert alert-secondary'><i class='bi bi-gear'></i> Service non configuré</div>";
    }
    
    if ($status['active']) {
        $html = "<div class='alert alert-success'>";
        $html .= "<i class='bi bi-check-circle'></i> <strong>$serviceName</strong> est actif";
        
        if (isset($status['remaining_time'])) {
            $html .= "<br><small><i class='bi bi-clock'></i> Temps restant: " . $status['remaining_time']['formatted'];
            $html .= " (jusqu'au " . $status['end_time'] . ")</small>";
        }
        
        $html .= "</div>";
        return $html;
    } else {
        return "<div class='alert alert-warning'><i class='bi bi-x-circle'></i> <strong>$serviceName</strong> est inactif</div>";
    }
}

?>
