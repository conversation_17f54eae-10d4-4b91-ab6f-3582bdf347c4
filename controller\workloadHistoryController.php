<?php
/**
 * Contrôleur pour la gestion de l'historique de charge horaire
 */

require_once '../model/enseignantModel.php';
require_once '../config/db.php';

header('Content-Type: application/json');

// Vérifier l'authentification
session_start();
if (!isset($_SESSION['user']) || !isset($_SESSION['user']['username'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Non authentifié']);
    exit();
}

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'getTeacherWorkloadHistory':
            handleGetTeacherWorkloadHistory();
            break;
            
        case 'getTeacherWorkloadByYear':
            handleGetTeacherWorkloadByYear();
            break;
            
        case 'getAvailableYears':
            handleGetAvailableYears();
            break;
            
        case 'getWorkloadBreakdown':
            handleGetWorkloadBreakdown();
            break;
            
        case 'initializeNewYear':
            handleInitializeNewYear();
            break;
            
        case 'getCurrentAcademicYear':
            handleGetCurrentAcademicYear();
            break;
            
        case 'getAllTeachersWorkload':
            handleGetAllTeachersWorkload();
            break;
            
        case 'exportWorkloadData':
            handleExportWorkloadData();
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Action non reconnue']);
            break;
    }
} catch (Exception $e) {
    error_log("Error in workloadHistoryController: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Erreur serveur: ' . $e->getMessage()]);
}

/**
 * Récupérer l'historique de charge horaire d'un enseignant
 */
function handleGetTeacherWorkloadHistory() {
    $teacherId = $_GET['teacher_id'] ?? null;
    
    if (!$teacherId) {
        http_response_code(400);
        echo json_encode(['error' => 'ID enseignant requis']);
        return;
    }
    
    $history = getTeacherWorkloadHistory($teacherId);
    
    if ($history === false) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération de l\'historique']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $history
    ]);
}

/**
 * Récupérer la charge horaire d'un enseignant pour une année spécifique
 */
function handleGetTeacherWorkloadByYear() {
    $teacherId = $_GET['teacher_id'] ?? null;
    $academicYear = $_GET['academic_year'] ?? null;
    
    if (!$teacherId || !$academicYear) {
        http_response_code(400);
        echo json_encode(['error' => 'ID enseignant et année universitaire requis']);
        return;
    }
    
    $workload = getTeacherWorkloadHoursByYear($teacherId, $academicYear);
    
    if ($workload === false) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération de la charge horaire']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'data' => [
            'teacher_id' => $teacherId,
            'academic_year' => $academicYear,
            'workload_hours' => $workload
        ]
    ]);
}

/**
 * Récupérer toutes les années universitaires disponibles
 */
function handleGetAvailableYears() {
    $years = getAvailableAcademicYears();
    
    if ($years === false) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des années']);
        return;
    }
    
    // Ajouter l'année courante si elle n'existe pas
    $currentYear = getCurrentAcademicYear();
    if (!in_array($currentYear, $years)) {
        array_unshift($years, $currentYear);
    }
    
    echo json_encode([
        'success' => true,
        'data' => $years,
        'current_year' => $currentYear
    ]);
}

/**
 * Récupérer le détail de la charge horaire d'un enseignant
 */
function handleGetWorkloadBreakdown() {
    $teacherId = $_GET['teacher_id'] ?? null;
    $academicYear = $_GET['academic_year'] ?? getCurrentAcademicYear();
    
    if (!$teacherId) {
        http_response_code(400);
        echo json_encode(['error' => 'ID enseignant requis']);
        return;
    }
    
    $breakdown = getTeacherWorkloadBreakdown($teacherId, $academicYear);
    
    if ($breakdown === false) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération du détail']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'data' => $breakdown
    ]);
}

/**
 * Initialiser une nouvelle année universitaire
 */
function handleInitializeNewYear() {
    // Vérifier les permissions (admin ou chef de département)
    $userRole = $_SESSION['user']['role'] ?? '';
    if (!in_array($userRole, ['admin', 'chef de departement'])) {
        http_response_code(403);
        echo json_encode(['error' => 'Permissions insuffisantes']);
        return;
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $academicYear = $input['academic_year'] ?? null;
    
    if (!$academicYear) {
        http_response_code(400);
        echo json_encode(['error' => 'Année universitaire requise']);
        return;
    }
    
    // Valider le format de l'année (YYYY-YYYY)
    if (!preg_match('/^\d{4}-\d{4}$/', $academicYear)) {
        http_response_code(400);
        echo json_encode(['error' => 'Format d\'année invalide (attendu: YYYY-YYYY)']);
        return;
    }
    
    $result = initializeAcademicYearWorkload($academicYear);
    
    if (!$result) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de l\'initialisation']);
        return;
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Année universitaire $academicYear initialisée avec succès"
    ]);
}

/**
 * Récupérer l'année universitaire actuelle
 */
function handleGetCurrentAcademicYear() {
    $currentYear = getCurrentAcademicYear();
    
    echo json_encode([
        'success' => true,
        'data' => [
            'academic_year' => $currentYear,
            'start_year' => explode('-', $currentYear)[0],
            'end_year' => explode('-', $currentYear)[1]
        ]
    ]);
}

/**
 * Récupérer la charge horaire de tous les enseignants pour une année
 */
function handleGetAllTeachersWorkload() {
    $academicYear = $_GET['academic_year'] ?? getCurrentAcademicYear();
    
    $conn = getConnection();
    if (!$conn) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur de connexion à la base de données']);
        return;
    }
    
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    
    $query = "SELECT 
                e.id_enseignant,
                e.nom,
                e.prenom,
                e.role,
                d.nom_dep as departement,
                s.nom as specialite,
                COALESCE(ewh.charge_horaire_accomplie, 0) as charge_horaire_accomplie,
                COALESCE(ewh.charge_horaire_cours, 0) as charge_horaire_cours,
                COALESCE(ewh.charge_horaire_td, 0) as charge_horaire_td,
                COALESCE(ewh.charge_horaire_tp, 0) as charge_horaire_tp,
                COALESCE(ewh.nombre_modules, 0) as nombre_modules,
                ewh.derniere_mise_a_jour
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              LEFT JOIN enseignant_workload_history ewh ON e.id_enseignant = ewh.id_enseignant 
                AND ewh.annee_universitaire = '$academicYear'
              ORDER BY e.nom, e.prenom";
    
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        mysqli_close($conn);
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des données']);
        return;
    }
    
    $teachers = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);
    
    echo json_encode([
        'success' => true,
        'data' => $teachers,
        'academic_year' => $academicYear,
        'total_teachers' => count($teachers)
    ]);
}

/**
 * Exporter les données de charge horaire
 */
function handleExportWorkloadData() {
    $academicYear = $_GET['academic_year'] ?? getCurrentAcademicYear();
    $format = $_GET['format'] ?? 'json';
    
    // Récupérer les données
    $conn = getConnection();
    if (!$conn) {
        http_response_code(500);
        echo json_encode(['error' => 'Erreur de connexion à la base de données']);
        return;
    }
    
    $academicYear = mysqli_real_escape_string($conn, $academicYear);
    
    $query = "SELECT 
                e.nom,
                e.prenom,
                e.role,
                d.nom_dep as departement,
                s.nom as specialite,
                COALESCE(ewh.charge_horaire_accomplie, 0) as total_heures,
                COALESCE(ewh.charge_horaire_cours, 0) as heures_cours,
                COALESCE(ewh.charge_horaire_td, 0) as heures_td,
                COALESCE(ewh.charge_horaire_tp, 0) as heures_tp,
                COALESCE(ewh.nombre_modules, 0) as nombre_modules,
                ewh.derniere_mise_a_jour
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              LEFT JOIN enseignant_workload_history ewh ON e.id_enseignant = ewh.id_enseignant 
                AND ewh.annee_universitaire = '$academicYear'
              ORDER BY e.nom, e.prenom";
    
    $result = mysqli_query($conn, $query);
    
    if (!$result) {
        mysqli_close($conn);
        http_response_code(500);
        echo json_encode(['error' => 'Erreur lors de la récupération des données']);
        return;
    }
    
    $data = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);
    
    if ($format === 'csv') {
        // Export CSV
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="charge_horaire_' . $academicYear . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // En-têtes CSV
        fputcsv($output, [
            'Nom', 'Prénom', 'Rôle', 'Département', 'Spécialité',
            'Total Heures', 'Heures Cours', 'Heures TD', 'Heures TP',
            'Nombre Modules', 'Dernière MAJ'
        ]);
        
        // Données
        foreach ($data as $row) {
            fputcsv($output, $row);
        }
        
        fclose($output);
    } else {
        // Export JSON
        echo json_encode([
            'success' => true,
            'data' => $data,
            'academic_year' => $academicYear,
            'export_date' => date('Y-m-d H:i:s'),
            'total_records' => count($data)
        ]);
    }
}
?>
