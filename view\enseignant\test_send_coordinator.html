<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Envoi Coordinateur</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .compact-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .grades-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #4a89dc;
            color: white;
        }
        .validation-status.validated {
            color: green;
            font-weight: bold;
        }
        .validation-status.rejected {
            color: red;
            font-weight: bold;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .hidden-elements {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Test d'Envoi au Coordinateur</h1>
    
    <!-- Configuration des paramètres -->
    <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h3>Configuration des Paramètres</h3>
        <div class="row" style="display: flex; gap: 20px;">
            <div class="form-group" style="flex: 1;">
                <label for="module_id">Module ID:</label>
                <input type="text" id="module_id" value="1">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="filiere_id">Filière ID:</label>
                <input type="text" id="filiere_id" value="1">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="niveau_id">Niveau ID:</label>
                <input type="text" id="niveau_id" value="1">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="semestre_id">Semestre ID:</label>
                <input type="text" id="semestre_id" value="1">
            </div>
        </div>
        <div class="row" style="display: flex; gap: 20px;">
            <div class="form-group" style="flex: 1;">
                <label for="session">Session:</label>
                <select id="session">
                    <option value="normale">Normale</option>
                    <option value="rattrapage">Rattrapage</option>
                </select>
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="module_name">Nom Module:</label>
                <input type="text" id="module_name" value="Programmation Web">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="filiere_name">Nom Filière:</label>
                <input type="text" id="filiere_name" value="Informatique">
            </div>
            <div class="form-group" style="flex: 1;">
                <label for="teacher_name">Nom Enseignant:</label>
                <input type="text" id="teacher_name" value="Dr. Ahmed ALAMI">
            </div>
        </div>
    </div>

    <!-- Éléments cachés pour simuler la page student_grades -->
    <div class="hidden-elements">
        <div id="niveau-value">L3</div>
        <div id="semestre-value">S5</div>
        <div id="session-value">Normale</div>
    </div>

    <div class="compact-header">
        <h2>Université Mohammed V</h2>
        <p>École Nationale Supérieure d'Informatique et d'Analyse des Systèmes</p>
        <p><strong>Module:</strong> <span id="display-module">Programmation Web</span> | <strong>Filière:</strong> <span id="display-filiere">Informatique</span> | <strong>Session:</strong> <span id="display-session">Normale</span></p>
    </div>

    <div class="grades-container">
        <h3>Notes des Étudiants</h3>
        <table id="students-list">
            <thead>
                <tr>
                    <th>N°</th>
                    <th>CNE</th>
                    <th>Nom</th>
                    <th>Prénom</th>
                    <th>Note</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>12345678</td>
                    <td>ALAMI</td>
                    <td>Ahmed</td>
                    <td>15.5</td>
                    <td><span class="validation-status validated">V</span></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>87654321</td>
                    <td>BENALI</td>
                    <td>Fatima</td>
                    <td>11.0</td>
                    <td><span class="validation-status rejected">R</span></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>11223344</td>
                    <td>CHAKIR</td>
                    <td>Omar</td>
                    <td>18.0</td>
                    <td><span class="validation-status validated">V</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="margin-top: 20px;">
        <button class="btn" onclick="testSendToCoordinator()">Test Envoi au Coordinateur</button>
        <button class="btn" onclick="updateDisplay()">Mettre à jour l'affichage</button>
    </div>

    <div id="result" style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; display: none;">
        <h4>Résultat:</h4>
        <p id="result-text"></p>
        <pre id="result-details" style="background-color: #e9ecef; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;"></pre>
    </div>

    <script>
        function showResult(message, details = '', isError = false) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            const resultDetails = document.getElementById('result-details');
            
            resultText.textContent = message;
            resultDetails.textContent = details;
            resultDiv.style.display = 'block';
            resultDiv.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
            resultDiv.style.color = isError ? '#721c24' : '#155724';
            
            console.log(isError ? 'ERROR:' : 'SUCCESS:', message);
            if (details) console.log('Details:', details);
        }

        function updateDisplay() {
            // Mettre à jour l'affichage avec les valeurs des champs
            document.getElementById('display-module').textContent = document.getElementById('module_name').value;
            document.getElementById('display-filiere').textContent = document.getElementById('filiere_name').value;
            document.getElementById('display-session').textContent = document.getElementById('session').value;
            document.getElementById('session-value').textContent = document.getElementById('session').value;
            
            showResult('Affichage mis à jour avec succès');
        }

        function testSendToCoordinator() {
            showResult('Test d\'envoi au coordinateur en cours...');
            
            try {
                // Get the elements to include in the PDF
                const compactHeader = document.querySelector('.compact-header');
                const gradesContainer = document.querySelector('.grades-container');

                if (!compactHeader || !gradesContainer) {
                    showResult('Erreur: Éléments manquants pour la génération du PDF', '', true);
                    return;
                }

                // Create a clone of the elements
                const pdfContent = document.createElement('div');
                pdfContent.appendChild(compactHeader.cloneNode(true));
                pdfContent.appendChild(gradesContainer.cloneNode(true));

                // PDF options
                const options = {
                    margin: 10,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { scale: 2, useCORS: true },
                    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                };

                // Generate PDF as blob
                html2pdf().from(pdfContent).set(options).outputPdf('blob').then(blob => {
                    // Convert blob to base64
                    const reader = new FileReader();
                    reader.readAsDataURL(blob);
                    reader.onloadend = function() {
                        const base64data = reader.result;

                        // Get values from form
                        const moduleId = document.getElementById('module_id').value;
                        const filiereId = document.getElementById('filiere_id').value;
                        const niveauId = document.getElementById('niveau_id').value;
                        const semestreId = document.getElementById('semestre_id').value;
                        const session = document.getElementById('session').value;
                        const moduleName = document.getElementById('module_name').value;
                        const filiereName = document.getElementById('filiere_name').value;
                        const teacherName = document.getElementById('teacher_name').value;

                        // Get display values
                        const niveauValue = document.getElementById('niveau-value').textContent;
                        const semestreValue = document.getElementById('semestre-value').textContent;
                        const sessionValue = document.getElementById('session-value').textContent;

                        // Prepare data to send to server
                        const data = {
                            pdf_content: base64data,
                            module_id: moduleId,
                            filiere_id: filiereId,
                            niveau_id: niveauId,
                            semestre_id: semestreId,
                            session: sessionValue,
                            module_name: moduleName,
                            filiere_name: filiereName,
                            niveau_name: niveauValue,
                            semestre_name: semestreValue,
                            teacher_name: teacherName,
                            teacher_id: 1 // Test teacher ID
                        };

                        console.log('Sending data to coordinator:', data);

                        // Send data to server
                        fetch('../../route/noteRoute.php?action=send_to_coordinator', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(data)
                        })
                        .then(response => {
                            console.log('Response status:', response.status);
                            if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(responseData => {
                            console.log('Response data:', responseData);
                            if (responseData.success) {
                                showResult('Notes envoyées au coordinateur avec succès !', JSON.stringify(responseData, null, 2));
                            } else {
                                showResult('Erreur lors de l\'envoi des notes: ' + (responseData.error || 'Erreur inconnue'), JSON.stringify(responseData, null, 2), true);
                            }
                        })
                        .catch(error => {
                            console.error('Error sending PDF to coordinator:', error);
                            showResult('Erreur lors de l\'envoi des notes au coordinateur: ' + error.message, error.stack, true);
                        });
                    };
                }).catch(error => {
                    console.error('Error generating PDF blob:', error);
                    showResult('Erreur lors de la génération du PDF blob: ' + error.message, error.stack, true);
                });

            } catch (error) {
                console.error('Unexpected error:', error);
                showResult('Erreur inattendue: ' + error.message, error.stack, true);
            }
        }

        // Mettre à jour l'affichage au chargement
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay();
        });
    </script>
</body>
</html>
