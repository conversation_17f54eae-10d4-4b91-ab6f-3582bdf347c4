<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PDF Generation</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .compact-header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .grades-container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #4a89dc;
            color: white;
        }
        .validation-status.validated {
            color: green;
            font-weight: bold;
        }
        .validation-status.rejected {
            color: red;
            font-weight: bold;
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Test de Génération PDF</h1>
    
    <div class="compact-header">
        <h2>Université Mohammed V</h2>
        <p>École Nationale Supérieure d'Informatique et d'Analyse des Systèmes</p>
        <p><strong>Module:</strong> Programmation Web | <strong>Filière:</strong> Informatique | <strong>Session:</strong> Normale</p>
    </div>

    <div class="grades-container">
        <h3>Notes des Étudiants</h3>
        <table id="students-list">
            <thead>
                <tr>
                    <th>N°</th>
                    <th>CNE</th>
                    <th>Nom</th>
                    <th>Prénom</th>
                    <th>Note</th>
                    <th>Statut</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>1</td>
                    <td>12345678</td>
                    <td>ALAMI</td>
                    <td>Ahmed</td>
                    <td>15.5</td>
                    <td><span class="validation-status validated">V</span></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>87654321</td>
                    <td>BENALI</td>
                    <td>Fatima</td>
                    <td>11.0</td>
                    <td><span class="validation-status rejected">R</span></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>11223344</td>
                    <td>CHAKIR</td>
                    <td>Omar</td>
                    <td>18.0</td>
                    <td><span class="validation-status validated">V</span></td>
                </tr>
            </tbody>
        </table>
    </div>

    <div style="margin-top: 20px;">
        <button class="btn" onclick="testPDFGeneration()">Générer PDF Test</button>
        <button class="btn" onclick="testPDFBlob()">Test PDF Blob</button>
    </div>

    <div id="result" style="margin-top: 20px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; display: none;">
        <h4>Résultat:</h4>
        <p id="result-text"></p>
    </div>

    <script>
        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            
            resultText.textContent = message;
            resultDiv.style.display = 'block';
            resultDiv.style.backgroundColor = isError ? '#f8d7da' : '#d4edda';
            resultDiv.style.color = isError ? '#721c24' : '#155724';
            
            console.log(isError ? 'ERROR:' : 'SUCCESS:', message);
        }

        function testPDFGeneration() {
            showResult('Génération du PDF en cours...');
            
            try {
                // Get the elements to include in the PDF
                const compactHeader = document.querySelector('.compact-header');
                const gradesContainer = document.querySelector('.grades-container');

                if (!compactHeader || !gradesContainer) {
                    showResult('Erreur: Éléments manquants pour la génération du PDF', true);
                    return;
                }

                // Create a clone of the elements
                const pdfContent = document.createElement('div');
                pdfContent.appendChild(compactHeader.cloneNode(true));
                pdfContent.appendChild(gradesContainer.cloneNode(true));

                // PDF options
                const options = {
                    margin: 10,
                    filename: 'test_notes.pdf',
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { scale: 2, useCORS: true },
                    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                };

                // Generate PDF
                html2pdf().from(pdfContent).set(options).save().then(() => {
                    showResult('PDF généré avec succès !');
                }).catch(error => {
                    console.error('Error generating PDF:', error);
                    showResult('Erreur lors de la génération du PDF: ' + error.message, true);
                });

            } catch (error) {
                console.error('Unexpected error:', error);
                showResult('Erreur inattendue: ' + error.message, true);
            }
        }

        function testPDFBlob() {
            showResult('Test de génération PDF Blob en cours...');
            
            try {
                // Get the elements to include in the PDF
                const compactHeader = document.querySelector('.compact-header');
                const gradesContainer = document.querySelector('.grades-container');

                if (!compactHeader || !gradesContainer) {
                    showResult('Erreur: Éléments manquants pour la génération du PDF', true);
                    return;
                }

                // Create a clone of the elements
                const pdfContent = document.createElement('div');
                pdfContent.appendChild(compactHeader.cloneNode(true));
                pdfContent.appendChild(gradesContainer.cloneNode(true));

                // PDF options
                const options = {
                    margin: 10,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { scale: 2, useCORS: true },
                    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                };

                // Generate PDF as blob
                html2pdf().from(pdfContent).set(options).outputPdf('blob').then(blob => {
                    showResult(`PDF Blob généré avec succès ! Taille: ${blob.size} bytes`);
                    
                    // Test conversion to base64
                    const reader = new FileReader();
                    reader.readAsDataURL(blob);
                    reader.onloadend = function() {
                        const base64data = reader.result;
                        console.log('Base64 data length:', base64data.length);
                        showResult(`PDF Blob et Base64 générés avec succès ! Taille blob: ${blob.size} bytes, Base64 length: ${base64data.length}`);
                    };
                }).catch(error => {
                    console.error('Error generating PDF blob:', error);
                    showResult('Erreur lors de la génération du PDF blob: ' + error.message, true);
                });

            } catch (error) {
                console.error('Unexpected error:', error);
                showResult('Erreur inattendue: ' + error.message, true);
            }
        }
    </script>
</body>
</html>
