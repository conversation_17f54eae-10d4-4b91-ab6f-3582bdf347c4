<?php
require_once __DIR__ . '/../config/db.php';

/**
 * Ensure the enseignant table has the charge_horaire_accomplie column
 * @return bool True if column exists or was created successfully
 */
function ensureChargeHoraireAccomplieColumn() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in ensureChargeHoraireAccomplieColumn");
        return false;
    }

    // Check if the column exists
    $columnExists = mysqli_query($conn, "SHOW COLUMNS FROM enseignant LIKE 'charge_horaire_accomplie'");

    if (!$columnExists) {
        error_log("Error checking for charge_horaire_accomplie column: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    $exists = mysqli_num_rows($columnExists) > 0;

    if (!$exists) {
        error_log("charge_horaire_accomplie column does not exist in enseignant table, creating it now");

        // Add the column with default value 0
        $addColumnSQL = "ALTER TABLE enseignant ADD COLUMN charge_horaire_accomplie INT(11) NOT NULL DEFAULT 0 COMMENT 'Total des heures d\'enseignement assignées'";

        $result = mysqli_query($conn, $addColumnSQL);

        if (!$result) {
            error_log("Error adding charge_horaire_accomplie column to enseignant table: " . mysqli_error($conn));
            mysqli_close($conn);
            return false;
        }

        error_log("charge_horaire_accomplie column added successfully to enseignant table");
    } else {
        error_log("charge_horaire_accomplie column exists in enseignant table");
    }

    mysqli_close($conn);
    return true;
}

/**
 * Update teacher's accomplished workload hours (uses new historical system)
 * @param int $teacherId Teacher ID
 * @param int $hours Hours to add (can be negative to subtract)
 * @param string $academicYear Academic year (optional, defaults to current)
 * @return bool|array True on success, error array on failure
 */
function updateTeacherWorkloadHours($teacherId, $hours, $academicYear = null) {
    if ($academicYear === null) {
        $academicYear = getCurrentAcademicYear();
    }

    return updateTeacherWorkloadHoursByYear($teacherId, $hours, $academicYear);
}

/**
 * Update teacher's workload hours for a specific academic year
 * @param int $teacherId Teacher ID
 * @param int $hours Hours to add (can be negative to subtract)
 * @param string $academicYear Academic year (format: 2024-2025)
 * @return bool|array True on success, error array on failure
 */
function updateTeacherWorkloadHoursByYear($teacherId, $hours, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateTeacherWorkloadHoursByYear");
        return ["error" => "Database connection error"];
    }

    // Validate parameters
    if (empty($teacherId)) {
        error_log("Empty teacher ID provided to updateTeacherWorkloadHoursByYear");
        mysqli_close($conn);
        return ["error" => "Teacher ID cannot be empty"];
    }

    if (!is_numeric($hours)) {
        error_log("Invalid hours value provided to updateTeacherWorkloadHoursByYear: " . $hours);
        mysqli_close($conn);
        return ["error" => "Hours must be a numeric value"];
    }

    try {
        // Start transaction
        mysqli_begin_transaction($conn);

        $teacherId = mysqli_real_escape_string($conn, $teacherId);
        $hours = mysqli_real_escape_string($conn, $hours);
        $academicYear = mysqli_real_escape_string($conn, $academicYear);

        // Insert or update workload record
        $upsertQuery = "INSERT INTO enseignant_workload_history
                       (id_enseignant, annee_universitaire, charge_horaire_accomplie)
                       VALUES ('$teacherId', '$academicYear', '$hours')
                       ON DUPLICATE KEY UPDATE
                       charge_horaire_accomplie = charge_horaire_accomplie + VALUES(charge_horaire_accomplie),
                       updated_at = CURRENT_TIMESTAMP";

        error_log("Updating teacher workload: teacherId=$teacherId, hours=$hours, year=$academicYear");

        $result = mysqli_query($conn, $upsertQuery);

        if (!$result) {
            throw new Exception("Error updating teacher workload: " . mysqli_error($conn));
        }

        // Update legacy column if it's the current year
        if ($academicYear === getCurrentAcademicYear()) {
            $legacyUpdateQuery = "UPDATE enseignant
                                 SET charge_horaire_accomplie = charge_horaire_accomplie + $hours
                                 WHERE id_enseignant = '$teacherId'";

            $legacyResult = mysqli_query($conn, $legacyUpdateQuery);
            if (!$legacyResult) {
                error_log("Warning: Failed to update legacy workload column: " . mysqli_error($conn));
            }
        }

        // Get the updated workload for logging
        $selectQuery = "SELECT charge_horaire_accomplie
                       FROM enseignant_workload_history
                       WHERE id_enseignant = '$teacherId'
                       AND annee_universitaire = '$academicYear'";
        $selectResult = mysqli_query($conn, $selectQuery);

        if ($selectResult && mysqli_num_rows($selectResult) > 0) {
            $row = mysqli_fetch_assoc($selectResult);
            $newWorkload = $row['charge_horaire_accomplie'];
            error_log("Teacher $teacherId workload updated successfully for $academicYear. New total: $newWorkload hours");
        }

        mysqli_commit($conn);
        mysqli_close($conn);
        return true;

    } catch (Exception $e) {
        mysqli_rollback($conn);
        error_log("Error in updateTeacherWorkloadHoursByYear: " . $e->getMessage());
        mysqli_close($conn);
        return ["error" => $e->getMessage()];
    }
}

/**
 * Get teacher's current workload hours
 * @param int $teacherId Teacher ID
 * @return int|false Current workload hours or false on error
 */
function getTeacherWorkloadHours($teacherId) {
    // Use the new historical system
    return getTeacherWorkloadHoursByYear($teacherId, getCurrentAcademicYear());
}

/**
 * Get current academic year in format YYYY-YYYY
 * @return string Current academic year
 */
function getCurrentAcademicYear() {
    $currentYear = date('Y');
    $currentMonth = date('n');

    // Academic year starts in September
    if ($currentMonth >= 9) {
        return $currentYear . '-' . ($currentYear + 1);
    } else {
        return ($currentYear - 1) . '-' . $currentYear;
    }
}

/**
 * Get teacher's workload hours for a specific academic year
 * @param int $teacherId Teacher ID
 * @param string $academicYear Academic year (format: 2024-2025)
 * @return int|false Workload hours or false on error
 */
function getTeacherWorkloadHoursByYear($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherWorkloadHoursByYear");
        return false;
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    $query = "SELECT charge_horaire_accomplie
              FROM enseignant_workload_history
              WHERE id_enseignant = '$teacherId'
              AND annee_universitaire = '$academicYear'";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting teacher workload by year: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    if (mysqli_num_rows($result) === 0) {
        // No record found, return 0 (new teacher or new year)
        mysqli_close($conn);
        return 0;
    }

    $row = mysqli_fetch_assoc($result);
    mysqli_close($conn);

    return (int)$row['charge_horaire_accomplie'];
}

function getAllEnseignants() {
    $conn = getConnection();
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id";
    $result = mysqli_query($conn, $query);
    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

/**
 * Get teacher's workload history for all years
 * @param int $teacherId Teacher ID
 * @return array|false Array of workload history or false on error
 */
function getTeacherWorkloadHistory($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherWorkloadHistory");
        return false;
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    $query = "SELECT * FROM enseignant_workload_history
              WHERE id_enseignant = '$teacherId'
              ORDER BY annee_universitaire DESC";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting teacher workload history: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    $history = mysqli_fetch_all($result, MYSQLI_ASSOC);
    mysqli_close($conn);

    return $history;
}

/**
 * Get all available academic years in the system
 * @return array|false Array of academic years or false on error
 */
function getAvailableAcademicYears() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAvailableAcademicYears");
        return false;
    }

    $query = "SELECT DISTINCT annee_universitaire
              FROM enseignant_workload_history
              ORDER BY annee_universitaire DESC";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting available academic years: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    $years = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $years[] = $row['annee_universitaire'];
    }

    mysqli_close($conn);
    return $years;
}

/**
 * Get detailed workload breakdown for a teacher in a specific year
 * @param int $teacherId Teacher ID
 * @param string $academicYear Academic year
 * @return array|false Detailed workload breakdown or false on error
 */
function getTeacherWorkloadBreakdown($teacherId, $academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherWorkloadBreakdown");
        return false;
    }

    $teacherId = mysqli_real_escape_string($conn, $teacherId);
    $academicYear = mysqli_real_escape_string($conn, $academicYear);

    $query = "SELECT
                ewh.*,
                e.nom,
                e.prenom,
                e.role
              FROM enseignant_workload_history ewh
              JOIN enseignant e ON ewh.id_enseignant = e.id_enseignant
              WHERE ewh.id_enseignant = '$teacherId'
              AND ewh.annee_universitaire = '$academicYear'";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        error_log("Error getting teacher workload breakdown: " . mysqli_error($conn));
        mysqli_close($conn);
        return false;
    }

    if (mysqli_num_rows($result) === 0) {
        mysqli_close($conn);
        return null;
    }

    $breakdown = mysqli_fetch_assoc($result);
    mysqli_close($conn);

    return $breakdown;
}

/**
 * Initialize workload history for a new academic year
 * @param string $academicYear Academic year (format: 2024-2025)
 * @return bool True on success, false on error
 */
function initializeAcademicYearWorkload($academicYear) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in initializeAcademicYearWorkload");
        return false;
    }

    try {
        mysqli_begin_transaction($conn);

        $academicYear = mysqli_real_escape_string($conn, $academicYear);

        // Get all active teachers
        $teachersQuery = "SELECT id_enseignant FROM enseignant";
        $teachersResult = mysqli_query($conn, $teachersQuery);

        if (!$teachersResult) {
            throw new Exception("Error getting teachers list: " . mysqli_error($conn));
        }

        $insertedCount = 0;
        while ($teacher = mysqli_fetch_assoc($teachersResult)) {
            $teacherId = $teacher['id_enseignant'];

            // Insert initial record for each teacher
            $insertQuery = "INSERT IGNORE INTO enseignant_workload_history
                           (id_enseignant, annee_universitaire, charge_horaire_accomplie,
                            charge_horaire_cours, charge_horaire_td, charge_horaire_tp, nombre_modules)
                           VALUES ('$teacherId', '$academicYear', 0, 0, 0, 0, 0)";

            $insertResult = mysqli_query($conn, $insertQuery);
            if ($insertResult && mysqli_affected_rows($conn) > 0) {
                $insertedCount++;
            }
        }

        mysqli_commit($conn);
        mysqli_close($conn);

        error_log("Initialized workload for $insertedCount teachers for academic year $academicYear");
        return true;

    } catch (Exception $e) {
        mysqli_rollback($conn);
        mysqli_close($conn);
        error_log("Error in initializeAcademicYearWorkload: " . $e->getMessage());
        return false;
    }
}

/**
 * Récupère tous les enseignants d'un département spécifique
 *
 * @param int $departementId L'ID du département
 * @return array Liste des enseignants du département
 */
function getEnseignantsByDepartement($departementId) {
    $conn = getConnection();

    // Sanitize the input
    $departementId = mysqli_real_escape_string($conn, $departementId);

    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.id_departement = '$departementId'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error retrieving teachers: ' . mysqli_error($conn)];
    }

    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

function getEnseignantByCNI($cni) {
    $conn = getConnection();
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.CNI = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    return mysqli_fetch_assoc($result);
}

function createEnseignant($data) {
    try {
        $conn = getConnection();

        if (!$conn) {
            error_log("Erreur de connexion à la base de données dans createEnseignant");
            return false;
        }

        // Vérifier si l'enseignant existe déjà
        $cni = mysqli_real_escape_string($conn, $data['CNI']);
        $checkQuery = "SELECT COUNT(*) as count FROM enseignant WHERE CNI = '$cni'";
        $checkResult = mysqli_query($conn, $checkQuery);

        if ($checkResult) {
            $row = mysqli_fetch_assoc($checkResult);
            if ($row['count'] > 0) {
                error_log("Un enseignant avec le CNI $cni existe déjà");
                return false;
            }
        }

        // Préparer les données
        $id_departement = isset($data['department']) ? $data['department'] : (isset($data['id_departement']) ? $data['id_departement'] : null);
        $id_specialite = isset($data['specialite']) ? $data['specialite'] : (isset($data['id_specialite']) ? $data['id_specialite'] : null);

        // Traiter correctement les dates
        $date_naissance = null;
        if (isset($data['date_naissance']) && $data['date_naissance'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_naissance']);
            if ($date_obj) {
                $date_naissance = date_format($date_obj, 'Y-m-d');
            }
        }

        // Si la date de naissance est null, utiliser une date par défaut car le champ est NOT NULL
        if ($date_naissance === null) {
            $date_naissance = '1990-01-01'; // Date par défaut
        }

        // Traiter correctement la date de début
        $date_debut_travail = null;
        if (isset($data['date_debut_travail']) && $data['date_debut_travail'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_debut_travail']);
            if ($date_obj) {
                $date_debut_travail = date_format($date_obj, 'Y-m-d');
            }
        }

        // Si la date de début est toujours null, utiliser la date actuelle
        if ($date_debut_travail === null) {
            $date_debut_travail = date('Y-m-d');
        }

        // Traiter l'image de profil
        $profile_picture = isset($data['profile_picture']) ? $data['profile_picture'] : 'default.png';

        // Log des données pour le débogage
        error_log("Données reçues pour createEnseignant: " . json_encode($data));
        error_log("id_departement: " . $id_departement);
        error_log("id_specialite: " . $id_specialite);
        error_log("date_naissance: " . $date_naissance);
        error_log("date_debut_travail: " . $date_debut_travail);
        error_log("profile_picture: " . $profile_picture);

        $query = "INSERT INTO enseignant (CNI, nom, prenom, email, tele, date_naissance, lieu_naissance, sexe, ville, pays, role, id_departement, id_specialite, date_debut_travail, profile_picture)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $query);

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . mysqli_error($conn));
            return false;
        }

        // Préparer les valeurs pour éviter les erreurs de type
        $tele = isset($data['tele']) && $data['tele'] !== '' ? $data['tele'] : null;
        $lieu_naissance = isset($data['lieu_naissance']) && $data['lieu_naissance'] !== '' ? $data['lieu_naissance'] : 'Non spécifié';
        $ville = isset($data['ville']) && $data['ville'] !== '' ? $data['ville'] : 'Non spécifié';
        $pays = isset($data['pays']) && $data['pays'] !== '' ? $data['pays'] : 'Maroc';

        mysqli_stmt_bind_param($stmt, "sssssssssssiiis",
            $data['CNI'], $data['nom'], $data['prenom'], $data['email'], $tele,
            $date_naissance, $lieu_naissance, $data['sexe'], $ville,
            $pays, $data['role'], $id_departement, $id_specialite, $date_debut_travail, $profile_picture
        );

        $result = mysqli_stmt_execute($stmt);

        if (!$result) {
            $error = mysqli_stmt_error($stmt);
            $errno = mysqli_stmt_errno($stmt);
            error_log("Erreur lors de l'exécution de la requête createEnseignant: " . $error . " (Code: $errno)");
            error_log("Query was: " . $query);
            error_log("Parameters were: " . json_encode([
                'CNI' => $data['CNI'],
                'nom' => $data['nom'],
                'prenom' => $data['prenom'],
                'email' => $data['email'],
                'tele' => $data['tele'],
                'date_naissance' => $date_naissance,
                'lieu_naissance' => $data['lieu_naissance'],
                'sexe' => $data['sexe'],
                'ville' => $data['ville'],
                'pays' => $data['pays'],
                'role' => $data['role'],
                'id_departement' => $id_departement,
                'id_specialite' => $id_specialite,
                'date_debut_travail' => $date_debut_travail,
                'profile_picture' => $profile_picture
            ]));

            // Check for specific error types
            if ($errno == 1062) { // Duplicate entry
                error_log("Duplicate entry detected for CNI: " . $data['CNI']);
            } elseif ($errno == 1452) { // Foreign key constraint fails
                error_log("Foreign key constraint violation - check department ID: $id_departement and specialite ID: $id_specialite");
            }
        } else {
            error_log("Enseignant created successfully with CNI: " . $data['CNI']);
        }

        mysqli_stmt_close($stmt);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans createEnseignant: " . $e->getMessage());
        return false;
    }
}

function updateEnseignant($cni, $data) {
    try {
        $conn = getConnection();

        // Log des données reçues pour le débogage
        error_log("Données reçues pour updateEnseignant: " . json_encode($data));

        // Vérifier et préparer les données
        $nom = isset($data['nom']) ? $data['nom'] : '';
        $prenom = isset($data['prenom']) ? $data['prenom'] : '';
        $email = isset($data['email']) ? $data['email'] : '';
        $tele = isset($data['tele']) && $data['tele'] !== '' ? $data['tele'] : null;

        // Traiter correctement les dates
        $date_naissance = null;
        if (isset($data['date_naissance']) && $data['date_naissance'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_naissance']);
            if ($date_obj) {
                $date_naissance = date_format($date_obj, 'Y-m-d');
            }
        }

        $lieu_naissance = isset($data['lieu_naissance']) && $data['lieu_naissance'] !== '' ? $data['lieu_naissance'] : null;
        $sexe = isset($data['sexe']) ? $data['sexe'] : '';
        $ville = isset($data['ville']) && $data['ville'] !== '' ? $data['ville'] : null;
        $pays = isset($data['pays']) && $data['pays'] !== '' ? $data['pays'] : null;
        $role = isset($data['role']) ? $data['role'] : 'enseignant';

        // Gérer les clés étrangères
        $id_departement = isset($data['department']) ? $data['department'] : (isset($data['id_departement']) && $data['id_departement'] !== '' ? $data['id_departement'] : null);
        $id_specialite = isset($data['specialite']) ? $data['specialite'] : (isset($data['id_specialite']) && $data['id_specialite'] !== '' ? $data['id_specialite'] : null);

        // Traiter correctement la date de début
        $date_debut_travail = null;
        if (isset($data['date_debut_travail']) && $data['date_debut_travail'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_debut_travail']);
            if ($date_obj) {
                $date_debut_travail = date_format($date_obj, 'Y-m-d');
            }
        }

        // Si la date de début est toujours null, utiliser la date actuelle
        if ($date_debut_travail === null) {
            $date_debut_travail = date('Y-m-d');
        }

        // Traiter l'image de profil
        $profile_picture = isset($data['profile_picture']) ? $data['profile_picture'] : null;

        // Log des données préparées pour le débogage
        error_log("Données préparées pour updateEnseignant: nom=$nom, prenom=$prenom, email=$email, tele=$tele, date_naissance=$date_naissance, lieu_naissance=$lieu_naissance, sexe=$sexe, ville=$ville, pays=$pays, role=$role, id_departement=$id_departement, id_specialite=$id_specialite, date_debut_travail=$date_debut_travail, profile_picture=$profile_picture, cni=$cni");

        $query = "UPDATE enseignant SET nom=?, prenom=?, email=?, tele=?, date_naissance=?,
                  lieu_naissance=?, sexe=?, ville=?, pays=?, role=?, id_departement=?, id_specialite=?,
                  date_debut_travail=?, profile_picture=?
                  WHERE CNI=?";
        $stmt = mysqli_prepare($conn, $query);

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . mysqli_error($conn));
            return false;
        }

        mysqli_stmt_bind_param($stmt, "ssssssssssiisss",
            $nom, $prenom, $email, $tele, $date_naissance,
            $lieu_naissance, $sexe, $ville, $pays,
            $role, $id_departement, $id_specialite, $date_debut_travail, $profile_picture, $cni
        );

        $result = mysqli_stmt_execute($stmt);

        if (!$result) {
            error_log("Erreur lors de l'exécution de la requête: " . mysqli_stmt_error($stmt));
        }

        mysqli_stmt_close($stmt);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans updateEnseignant: " . $e->getMessage());
        return false;
    }
}

function deleteEnseignant($cni) {
    $conn = getConnection();
    $query = "DELETE FROM enseignant WHERE CNI=?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    return mysqli_stmt_execute($stmt);
}

/**
 * Search for teachers by name or part of name
 *
 * @param string $searchTerm The search term
 * @return array Array of matching teachers
 */
function searchEnseignants($searchTerm) {
    $conn = getConnection();

    // Sanitize the search term
    $searchTerm = mysqli_real_escape_string($conn, $searchTerm);

    // Search in both nom and prenom fields
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.nom LIKE '%$searchTerm%'
              OR e.prenom LIKE '%$searchTerm%'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error searching teachers: ' . mysqli_error($conn)];
    }

    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

/**
 * Format role name for display
 *
 * @param string $role Role from database
 * @return string Formatted role name
 */
function formatRoleName($role) {
    switch ($role) {
        case 'normal':
            return 'Enseignant';
        case 'chef de departement':
            return 'Chef de Département';
        case 'coordinateur':
            return 'Coordinateur';
        case 'vacataire':
            return 'Vacataire';
        case 'chef de filiere':
            return 'Chef de Filière';
        default:
            return ucfirst($role);
    }
}
/**
 * Get fields (filieres) associated with a teacher
 *
 * @param int $teacherId The teacher ID
 * @return array Array of fields associated with the teacher
 */
function getTeacherFields($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherFields");
        return ["error" => "Database connection error"];
    }

    // Sanitize the input
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // First, check if the enseignant_filiere table exists
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_filiere'");

    if (mysqli_num_rows($checkTable) == 0) {
        // If the table doesn't exist, create it
        error_log("enseignant_filiere table doesn't exist. Creating it.");

        $createTableQuery = "CREATE TABLE IF NOT EXISTS enseignant_filiere (
            id INT AUTO_INCREMENT PRIMARY KEY,
            id_enseignant INT NOT NULL,
            id_filiere INT NOT NULL,
            INDEX (id_enseignant),
            INDEX (id_filiere),
            UNIQUE KEY unique_enseignant_filiere (id_enseignant, id_filiere)
        )";

        $createResult = mysqli_query($conn, $createTableQuery);

        if (!$createResult) {
            error_log("Error creating enseignant_filiere table: " . mysqli_error($conn));
        } else {
            error_log("enseignant_filiere table created successfully.");

            // After creating the table, add associations for this teacher with all fields
            $allFieldsQuery = "SELECT id_filiere FROM filiere";
            $allFieldsResult = mysqli_query($conn, $allFieldsQuery);

            if ($allFieldsResult) {
                while ($row = mysqli_fetch_assoc($allFieldsResult)) {
                    $filiereId = $row['id_filiere'];
                    $insertQuery = "INSERT IGNORE INTO enseignant_filiere (id_enseignant, id_filiere) VALUES ('$teacherId', '$filiereId')";
                    mysqli_query($conn, $insertQuery);
                    error_log("Added association for teacher ID: $teacherId and field ID: $filiereId");
                }
            }
        }
    }

    // Check again if the table exists (it might have been created above)
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_filiere'");

    if (mysqli_num_rows($checkTable) > 0) {
        // If the table exists, use it to get the fields
        $query = "SELECT f.*
                  FROM filiere f
                  JOIN enseignant_filiere ef ON f.id_filiere = ef.id_filiere
                  WHERE ef.id_enseignant = '$teacherId'
                  ORDER BY f.nom_filiere";

        error_log("Using enseignant_filiere table to get fields for teacher ID: $teacherId");
    } else {
        // If the table doesn't exist, get fields based on modules assigned to the teacher
        // First, check if the affectation table exists
        $checkAffectationTable = mysqli_query($conn, "SHOW TABLES LIKE 'affectation'");

        if (mysqli_num_rows($checkAffectationTable) > 0) {
            // If the affectation table exists, use it to get the fields
            $query = "SELECT DISTINCT f.*
                      FROM filiere f
                      JOIN module m ON f.id_filiere = m.filiere_id
                      JOIN uniteenseignement ue ON m.id = ue.module_id
                      JOIN affectation a ON ue.id = a.unite_enseignement_id
                      WHERE a.id_enseignant = '$teacherId'
                      ORDER BY f.nom_filiere";
        } else {
            // If neither table exists, get fields based on seances
            $query = "SELECT DISTINCT f.*
                      FROM filiere f
                      JOIN module m ON f.id_filiere = m.filiere_id
                      JOIN seance s ON m.id_module = s.id_module
                      WHERE s.id_enseignant = '$teacherId'
                      ORDER BY f.nom_filiere";
        }
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherFields: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching fields: " . $error];
    }

    $fields = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $fields[] = $row;
    }

    mysqli_close($conn);
    return $fields;
}

/**
 * Get fields (filieres) associated with a teacher using the affectation table
 * This method is more accurate as it uses the actual teaching assignments
 *
 * @param int $teacherId The teacher ID
 * @return array Array of fields associated with the teacher
 */
function getTeacherFieldsFromAffectation($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherFieldsFromAffectation");
        return ["error" => "Database connection error"];
    }

    // Sanitize the input
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // Check if the affectation table exists
    $checkAffectationTable = mysqli_query($conn, "SHOW TABLES LIKE 'affectation'");

    if (mysqli_num_rows($checkAffectationTable) > 0) {
        // Check module table columns
        $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
        $moduleColumns = [];
        while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
            $moduleColumns[] = $col['Field'];
        }

        // Determine the correct field names
        $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
        $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' : 'id_filiere';

        // Check uniteenseignement table columns
        $checkUEColumns = mysqli_query($conn, "SHOW COLUMNS FROM uniteenseignement");
        $ueColumns = [];
        while ($col = mysqli_fetch_assoc($checkUEColumns)) {
            $ueColumns[] = $col['Field'];
        }

        $ueIdField = in_array('id', $ueColumns) ? 'id' : 'id_ue';
        $ueModuleIdField = in_array('module_id', $ueColumns) ? 'module_id' : 'id_module';

        // If the affectation table exists, use it to get the fields
        $query = "SELECT DISTINCT f.*
                  FROM filiere f
                  JOIN module m ON f.id_filiere = m.$filiereField
                  JOIN uniteenseignement ue ON m.$moduleIdField = ue.$ueModuleIdField
                  JOIN affectation a ON ue.$ueIdField = a.unite_enseignement_id
                  WHERE a.professeur_id = '$teacherId'
                  ORDER BY f.nom_filiere";

        error_log("Using affectation table to get fields for teacher ID: $teacherId");
    } else {
        // If the affectation table doesn't exist, try to use seances
        $checkSeanceTable = mysqli_query($conn, "SHOW TABLES LIKE 'seance'");

        if (mysqli_num_rows($checkSeanceTable) > 0) {
            // Check module table columns
            $checkModuleColumns = mysqli_query($conn, "SHOW COLUMNS FROM module");
            $moduleColumns = [];
            while ($col = mysqli_fetch_assoc($checkModuleColumns)) {
                $moduleColumns[] = $col['Field'];
            }

            // Determine the correct field names
            $moduleIdField = in_array('id', $moduleColumns) ? 'id' : 'id_module';
            $filiereField = in_array('filiere_id', $moduleColumns) ? 'filiere_id' : 'id_filiere';

            // Check seance table columns
            $checkSeanceColumns = mysqli_query($conn, "SHOW COLUMNS FROM seance");
            $seanceColumns = [];
            while ($col = mysqli_fetch_assoc($checkSeanceColumns)) {
                $seanceColumns[] = $col['Field'];
            }

            $seanceModuleIdField = in_array('id_module', $seanceColumns) ? 'id_module' : 'module_id';
            $seanceTeacherIdField = in_array('id_enseignant', $seanceColumns) ? 'id_enseignant' : 'enseignant_id';

            // Use seances to get the fields
            $query = "SELECT DISTINCT f.*
                      FROM filiere f
                      JOIN module m ON f.id_filiere = m.$filiereField
                      JOIN seance s ON m.$moduleIdField = s.$seanceModuleIdField
                      WHERE s.$seanceTeacherIdField = '$teacherId'
                      ORDER BY f.nom_filiere";

            error_log("Using seance table to get fields for teacher ID: $teacherId");
        } else {
            // If neither table exists, fall back to getting all fields
            $query = "SELECT * FROM filiere ORDER BY nom_filiere";
            error_log("No affectation or seance table found. Getting all fields.");
        }
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherFieldsFromAffectation: " . $error);

        // Try a fallback query to get all fields
        $fallbackQuery = "SELECT * FROM filiere ORDER BY nom_filiere";
        $fallbackResult = mysqli_query($conn, $fallbackQuery);

        if (!$fallbackResult) {
            $fallbackError = mysqli_error($conn);
            error_log("Error in fallback query: " . $fallbackError);
            mysqli_close($conn);
            return ["error" => "Error fetching fields: " . $fallbackError];
        }

        $fields = [];
        while ($row = mysqli_fetch_assoc($fallbackResult)) {
            $fields[] = $row;
        }
    } else {
        $fields = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $fields[] = $row;
        }
    }

    mysqli_close($conn);
    return $fields;
}
?>
