<?php
require_once "../config/db.php";

/**
 * Get all specialties
 *
 * @return array Array of specialties
 */
function getAllSpecialites() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getAllSpecialites");
        return ["error" => "Database connection error"];
    }

    $sql = "SELECT * FROM specialite ORDER BY nom";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getAllSpecialites: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching specialties: " . $error];
    }

    $specialites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $specialites[] = $row;
    }

    mysqli_close($conn);
    return $specialites;
}

/**
 * Get a specialty by ID
 *
 * @param int $id Specialty ID
 * @return array|null Specialty data or null if not found
 */
function getSpecialiteById($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getSpecialiteById");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $sql = "SELECT * FROM specialite WHERE id = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getSpecialiteById: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching specialty: " . $error];
    }

    $specialite = mysqli_fetch_assoc($result);
    mysqli_close($conn);
    
    if (!$specialite) {
        return ["error" => "Specialty not found"];
    }
    
    return $specialite;
}

/**
 * Create a new specialty
 *
 * @param array $data Specialty data
 * @return bool|array True on success, error array on failure
 */
function createSpecialite($data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in createSpecialite");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (empty($data['nom'])) {
        mysqli_close($conn);
        return ["error" => "Specialty name is required"];
    }

    $nom = mysqli_real_escape_string($conn, $data['nom']);
    $description = isset($data['description']) ? mysqli_real_escape_string($conn, $data['description']) : '';

    // Check if specialty already exists
    $checkSql = "SELECT COUNT(*) as count FROM specialite WHERE nom = '$nom'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if ($checkResult) {
        $row = mysqli_fetch_assoc($checkResult);
        if ($row['count'] > 0) {
            mysqli_close($conn);
            return ["error" => "A specialty with this name already exists"];
        }
    }

    // Insert the new specialty
    $sql = "INSERT INTO specialite (nom, description) VALUES ('$nom', '$description')";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in createSpecialite: " . $error);
        mysqli_close($conn);
        return ["error" => "Error creating specialty: " . $error];
    }

    mysqli_close($conn);
    return true;
}

/**
 * Update a specialty
 *
 * @param int $id Specialty ID
 * @param array $data Updated specialty data
 * @return bool|array True on success, error array on failure
 */
function updateSpecialite($id, $data) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in updateSpecialite");
        return ["error" => "Database connection error"];
    }

    // Validate required fields
    if (empty($data['nom'])) {
        mysqli_close($conn);
        return ["error" => "Specialty name is required"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $nom = mysqli_real_escape_string($conn, $data['nom']);
    $description = isset($data['description']) ? mysqli_real_escape_string($conn, $data['description']) : '';

    // Check if specialty exists
    $checkSql = "SELECT COUNT(*) as count FROM specialite WHERE id = '$id'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if ($checkResult) {
        $row = mysqli_fetch_assoc($checkResult);
        if ($row['count'] == 0) {
            mysqli_close($conn);
            return ["error" => "Specialty not found"];
        }
    }

    // Update the specialty
    $sql = "UPDATE specialite SET nom = '$nom', description = '$description' WHERE id = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in updateSpecialite: " . $error);
        mysqli_close($conn);
        return ["error" => "Error updating specialty: " . $error];
    }

    mysqli_close($conn);
    return true;
}

/**
 * Delete a specialty
 *
 * @param int $id Specialty ID
 * @return bool|array True on success, error array on failure
 */
function deleteSpecialite($id) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in deleteSpecialite");
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);

    // Check if specialty exists
    $checkSql = "SELECT COUNT(*) as count FROM specialite WHERE id = '$id'";
    $checkResult = mysqli_query($conn, $checkSql);
    
    if ($checkResult) {
        $row = mysqli_fetch_assoc($checkResult);
        if ($row['count'] == 0) {
            mysqli_close($conn);
            return ["error" => "Specialty not found"];
        }
    }

    // Check if specialty is in use
    $checkUsageSql = "SELECT COUNT(*) as count FROM enseignant WHERE id_specialite = '$id'";
    $checkUsageResult = mysqli_query($conn, $checkUsageSql);
    
    if ($checkUsageResult) {
        $row = mysqli_fetch_assoc($checkUsageResult);
        if ($row['count'] > 0) {
            mysqli_close($conn);
            return ["error" => "Cannot delete specialty because it is assigned to one or more teachers"];
        }
    }

    // Delete the specialty
    $sql = "DELETE FROM specialite WHERE id = '$id'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in deleteSpecialite: " . $error);
        mysqli_close($conn);
        return ["error" => "Error deleting specialty: " . $error];
    }

    mysqli_close($conn);
    return true;
}
