<?php
// Simple error log viewer
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Error Log Viewer</h1>";

// Try to find the PHP error log
$possibleLogPaths = [
    ini_get('error_log'),
    '/var/log/apache2/error.log',
    '/var/log/httpd/error_log',
    '/xampp/apache/logs/error.log',
    '/Applications/XAMPP/logs/php_error_log',
    'C:\xampp\apache\logs\error.log',
    'C:\wamp\logs\php_error.log',
    'error.log',
    '../error.log'
];

$logFound = false;
foreach ($possibleLogPaths as $logPath) {
    if ($logPath && file_exists($logPath) && is_readable($logPath)) {
        echo "<h2>Error Log: $logPath</h2>";
        
        // Read last 50 lines
        $lines = file($logPath);
        $lastLines = array_slice($lines, -50);
        
        echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; max-height: 400px; overflow-y: scroll;'>";
        echo "<pre>";
        foreach ($lastLines as $line) {
            // Highlight lines containing our keywords
            if (strpos($line, 'createEnseignant') !== false || 
                strpos($line, 'vacataire') !== false || 
                strpos($line, 'DOUAEVACAT') !== false) {
                echo "<span style='background: yellow;'>" . htmlspecialchars($line) . "</span>";
            } else {
                echo htmlspecialchars($line);
            }
        }
        echo "</pre>";
        echo "</div>";
        $logFound = true;
        break;
    }
}

if (!$logFound) {
    echo "<p style='color: orange;'>⚠ Could not find PHP error log. Tried these paths:</p>";
    echo "<ul>";
    foreach ($possibleLogPaths as $path) {
        echo "<li>" . ($path ?: 'NULL') . "</li>";
    }
    echo "</ul>";
    
    echo "<p>You can check your PHP configuration with:</p>";
    echo "<pre>echo ini_get('error_log');</pre>";
    echo "<p>Current value: " . (ini_get('error_log') ?: 'Not set') . "</p>";
}

// Also check if we can write to a custom log file
$customLogFile = 'debug.log';
if (is_writable('.') || is_writable($customLogFile)) {
    error_log("Test log entry from view_error_log.php at " . date('Y-m-d H:i:s'), 3, $customLogFile);
    
    if (file_exists($customLogFile)) {
        echo "<h2>Custom Debug Log: $customLogFile</h2>";
        echo "<div style='background: #f0f0f0; padding: 10px; border: 1px solid #ccc; max-height: 300px; overflow-y: scroll;'>";
        echo "<pre>" . htmlspecialchars(file_get_contents($customLogFile)) . "</pre>";
        echo "</div>";
    }
}

echo "<h2>Instructions</h2>";
echo "<p>1. Try to create a vacataire account now</p>";
echo "<p>2. Refresh this page to see new error messages</p>";
echo "<p>3. Look for lines highlighted in yellow that contain our keywords</p>";
?>
