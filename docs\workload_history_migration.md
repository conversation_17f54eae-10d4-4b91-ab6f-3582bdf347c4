# Migration vers le Suivi Historique de la Charge Horaire

## 📊 Analyse de la Structure Actuelle

### Structure Existante
```sql
-- Table enseignant (structure actuelle)
CREATE TABLE enseignant (
    id_enseignant INT(11) NOT NULL,
    CNI VARCHAR(20) NOT NULL,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    -- ... autres colonnes ...
    charge_horaire_accomplie INT(11) DEFAULT 0  -- ⚠️ PROBLÈME: Une seule valeur globale
);
```

### Problèmes Identifiés
1. **❌ Pas de suivi par année** : Une seule colonne pour toutes les années
2. **❌ Perte d'historique** : Données écrasées chaque année
3. **❌ Pas de traçabilité** : Impossible de voir l'évolution
4. **❌ Gestion complexe** : Remise à zéro manuelle nécessaire

## 🎯 Nouvelle Structure Proposée

### Table de Suivi Historique
```sql
-- Nouvelle table: enseignant_workload_history
CREATE TABLE enseignant_workload_history (
    id_workload INT(11) NOT NULL AUTO_INCREMENT,
    id_enseignant INT(11) NOT NULL,
    annee_universitaire VARCHAR(9) NOT NULL,  -- Format: 2024-2025
    charge_horaire_accomplie INT(11) NOT NULL DEFAULT 0,
    charge_horaire_cours INT(11) NOT NULL DEFAULT 0,
    charge_horaire_td INT(11) NOT NULL DEFAULT 0,
    charge_horaire_tp INT(11) NOT NULL DEFAULT 0,
    nombre_modules INT(11) NOT NULL DEFAULT 0,
    derniere_mise_a_jour TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id_workload),
    UNIQUE KEY unique_teacher_year (id_enseignant, annee_universitaire),
    FOREIGN KEY (id_enseignant) REFERENCES enseignant(id_enseignant) ON DELETE CASCADE,
    INDEX idx_teacher (id_enseignant),
    INDEX idx_year (annee_universitaire),
    INDEX idx_teacher_year (id_enseignant, annee_universitaire)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

### Vue pour Compatibilité
```sql
-- Vue pour maintenir la compatibilité avec l'ancien système
CREATE VIEW enseignant_current_workload AS
SELECT 
    e.id_enseignant,
    e.CNI,
    e.nom,
    e.prenom,
    e.email,
    e.role,
    e.id_specialite,
    e.id_departement,
    COALESCE(ewh.charge_horaire_accomplie, 0) as charge_horaire_accomplie,
    COALESCE(ewh.charge_horaire_cours, 0) as charge_horaire_cours,
    COALESCE(ewh.charge_horaire_td, 0) as charge_horaire_td,
    COALESCE(ewh.charge_horaire_tp, 0) as charge_horaire_tp,
    COALESCE(ewh.nombre_modules, 0) as nombre_modules,
    ewh.annee_universitaire
FROM enseignant e
LEFT JOIN enseignant_workload_history ewh ON e.id_enseignant = ewh.id_enseignant 
    AND ewh.annee_universitaire = (
        SELECT annee_universitaire 
        FROM enseignant_workload_history 
        WHERE id_enseignant = e.id_enseignant 
        ORDER BY annee_universitaire DESC 
        LIMIT 1
    );
```

## 🔄 Avantages de la Nouvelle Structure

### ✅ Suivi Historique Complet
- **Historique par année** : Données conservées pour chaque année universitaire
- **Évolution trackée** : Possibilité de voir la progression dans le temps
- **Audit trail** : Traçabilité complète des modifications

### ✅ Détail par Type d'Enseignement
- **Cours** : Heures de cours magistraux
- **TD** : Heures de travaux dirigés
- **TP** : Heures de travaux pratiques
- **Total** : Somme automatique de tous les types

### ✅ Statistiques Avancées
- **Nombre de modules** : Comptage automatique des modules assignés
- **Dernière mise à jour** : Timestamp de la dernière modification
- **Métriques détaillées** : Analyse fine de la charge de travail

### ✅ Performance Optimisée
- **Index appropriés** : Recherches rapides par enseignant et année
- **Contraintes d'intégrité** : Prévention des doublons et incohérences
- **Clés étrangères** : Maintien de l'intégrité référentielle

## 📋 Plan de Migration

### Phase 1: Création de la Nouvelle Structure
1. **Créer la table `enseignant_workload_history`**
2. **Créer la vue de compatibilité**
3. **Migrer les données existantes**

### Phase 2: Mise à Jour des Fonctions
1. **Adapter les fonctions de mise à jour**
2. **Créer les nouvelles fonctions historiques**
3. **Maintenir la compatibilité ascendante**

### Phase 3: Interface Utilisateur
1. **Ajouter la sélection d'année**
2. **Créer les vues historiques**
3. **Graphiques d'évolution**

### Phase 4: Tests et Validation
1. **Tests de migration**
2. **Validation des calculs**
3. **Tests de performance**

## 🛡️ Stratégie de Compatibilité

### Maintien de l'Ancien Système
- **Colonne existante conservée** : `charge_horaire_accomplie` reste en place
- **Synchronisation automatique** : Mise à jour bidirectionnelle
- **Transition progressive** : Basculement graduel vers le nouveau système

### Fonctions de Transition
```php
// Fonction de compatibilité pour l'ancien système
function updateTeacherWorkloadHours($teacherId, $hours) {
    // Mise à jour de l'ancien système
    updateLegacyWorkload($teacherId, $hours);
    
    // Mise à jour du nouveau système
    updateWorkloadHistory($teacherId, $hours, getCurrentAcademicYear());
}
```

## 📊 Fonctionnalités Nouvelles

### Consultation Historique
- **Sélection par année** : Dropdown avec toutes les années disponibles
- **Comparaison inter-années** : Graphiques d'évolution
- **Export de données** : Rapports détaillés par période

### Statistiques Avancées
- **Évolution de charge** : Tendances sur plusieurs années
- **Répartition par type** : Analyse cours/TD/TP
- **Benchmarking** : Comparaison entre enseignants

### Gestion Administrative
- **Archivage automatique** : Sauvegarde en fin d'année
- **Initialisation nouvelle année** : Remise à zéro automatique
- **Validation des données** : Contrôles d'intégrité

## 🎯 Résultat Attendu

### Pour les Enseignants
- **Historique personnel** : Consultation de leur évolution
- **Transparence** : Visibilité sur les calculs
- **Planification** : Aide à la gestion de carrière

### Pour les Chefs de Département
- **Suivi détaillé** : Monitoring précis des charges
- **Planification** : Aide à la répartition des cours
- **Reporting** : Rapports administratifs complets

### Pour l'Administration
- **Conformité** : Respect des obligations réglementaires
- **Audit** : Traçabilité complète des données
- **Analyse** : Statistiques institutionnelles

Cette migration permettra un suivi professionnel et historique de la charge horaire tout en maintenant la compatibilité avec le système existant.
