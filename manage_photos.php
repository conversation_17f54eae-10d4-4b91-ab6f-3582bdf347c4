<?php
// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Vérifier si une action a été demandée
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$file = isset($_GET['file']) ? $_GET['file'] : '';

// Inclure l'utilitaire de chemin
require_once __DIR__ . '/utils/path_utils.php';

// Obtenir le chemin de base du projet
$basePath = getBasePath();
$profileImgPath = 'view/assets/img/profile/';

// Définir les chemins possibles pour le dossier des photos
$paths = [
    // Chemins relatifs
    __DIR__ . '/' . $profileImgPath,
    // Chemins basés sur le document root
    $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $profileImgPath
];

// Trouver le premier chemin qui existe
$photoDir = null;
foreach ($paths as $path) {
    if (file_exists($path) && is_dir($path)) {
        $photoDir = $path;
        break;
    }
}

// Fonction pour se connecter à la base de données
function getConnection() {
    $host = 'localhost';
    $dbname = 'ensah';
    $username = 'root';
    $password = '';

    $conn = mysqli_connect($host, $username, $password, $dbname);

    if (!$conn) {
        die("Connexion échouée: " . mysqli_connect_error());
    }

    mysqli_set_charset($conn, "utf8");
    return $conn;
}

// Fonction pour récupérer toutes les photos de la base de données
function getAllPhotosFromDB() {
    $conn = getConnection();
    $query = "SELECT CNI, photo_url FROM admin WHERE photo_url != ''";
    $result = mysqli_query($conn, $query);

    $photos = [];
    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            $photos[] = $row;
        }
    }

    mysqli_close($conn);
    return $photos;
}

// Fonction pour supprimer une photo
function deletePhoto($file) {
    global $paths;

    $deleted = false;
    $message = '';

    // Essayer de supprimer le fichier avec tous les chemins possibles
    foreach ($paths as $path) {
        $fullPath = $path . $file;
        if (file_exists($fullPath)) {
            if (unlink($fullPath)) {
                $deleted = true;
                $message = "Photo supprimée avec succès: $fullPath";
                break;
            } else {
                $message = "Échec de la suppression: $fullPath";
            }
        }
    }

    if (!$deleted) {
        $message = "Impossible de trouver ou de supprimer la photo: $file";
    }

    return ['success' => $deleted, 'message' => $message];
}

// Traiter les actions
if ($action === 'delete' && !empty($file)) {
    $result = deletePhoto($file);
    echo "<div style='background-color: " . ($result['success'] ? "#d4edda" : "#f8d7da") . "; padding: 10px; margin-bottom: 20px;'>";
    echo $result['message'];
    echo "</div>";

    // Rediriger vers la liste après la suppression
    header("Refresh: 2; URL=manage_photos.php");
}

// Afficher l'interface
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des photos de profil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2 {
            color: #333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        img {
            max-width: 100px;
            max-height: 100px;
            border: 1px solid #ddd;
        }
        .actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            display: inline-block;
            padding: 5px 10px;
            background-color: #f44336;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .btn:hover {
            background-color: #d32f2f;
        }
        .info {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Gestion des photos de profil</h1>

    <div class="info">
        <p><strong>Dossier des photos:</strong> <?php echo $photoDir ? $photoDir : "Aucun dossier trouvé"; ?></p>
    </div>

    <h2>Photos dans le dossier</h2>
    <?php if ($photoDir): ?>
        <table>
            <thead>
                <tr>
                    <th>Nom du fichier</th>
                    <th>Aperçu</th>
                    <th>Taille</th>
                    <th>Date de modification</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $files = scandir($photoDir);
                $hasFiles = false;

                foreach ($files as $file) {
                    if ($file != '.' && $file != '..' && $file != '.htaccess' && !is_dir($photoDir . $file)) {
                        $hasFiles = true;
                        $filePath = $photoDir . $file;
                        $fileSize = filesize($filePath);
                        $fileDate = date("Y-m-d H:i:s", filemtime($filePath));

                        echo "<tr>";
                        echo "<td>$file</td>";
                        echo "<td><img src='serve_image.php?file=$file' alt='$file'></td>";
                        echo "<td>" . round($fileSize / 1024, 2) . " KB</td>";
                        echo "<td>$fileDate</td>";
                        echo "<td class='actions'>";
                        echo "<a href='manage_photos.php?action=delete&file=$file' class='btn' onclick='return confirm(\"Êtes-vous sûr de vouloir supprimer cette photo ?\")'>Supprimer</a>";
                        echo "</td>";
                        echo "</tr>";
                    }
                }

                if (!$hasFiles) {
                    echo "<tr><td colspan='5'>Aucun fichier trouvé dans le dossier</td></tr>";
                }
                ?>
            </tbody>
        </table>
    <?php else: ?>
        <p>Impossible de trouver le dossier des photos.</p>
    <?php endif; ?>

    <h2>Photos dans la base de données</h2>
    <table>
        <thead>
            <tr>
                <th>CNI</th>
                <th>URL de la photo</th>
                <th>Aperçu</th>
                <th>Existe sur le disque</th>
            </tr>
        </thead>
        <tbody>
            <?php
            $dbPhotos = getAllPhotosFromDB();

            if (!empty($dbPhotos)) {
                foreach ($dbPhotos as $photo) {
                    $photoExists = false;
                    $photoPath = '';

                    // Vérifier si le fichier existe sur le disque
                    if (strpos($photo['photo_url'], '/') !== false) {
                        // C'est un chemin relatif
                        $fileName = basename($photo['photo_url']);
                    } else {
                        // C'est juste un nom de fichier
                        $fileName = $photo['photo_url'];
                    }

                    foreach ($paths as $path) {
                        if (file_exists($path . $fileName)) {
                            $photoExists = true;
                            $photoPath = $path . $fileName;
                            break;
                        }
                    }

                    echo "<tr>";
                    echo "<td>{$photo['CNI']}</td>";
                    echo "<td>{$photo['photo_url']}</td>";
                    echo "<td><img src='serve_image.php?file=" . urlencode($photo['photo_url']) . "' alt='Photo'></td>";
                    echo "<td>" . ($photoExists ? "Oui ($photoPath)" : "Non") . "</td>";
                    echo "</tr>";
                }
            } else {
                echo "<tr><td colspan='4'>Aucune photo trouvée dans la base de données</td></tr>";
            }
            ?>
        </tbody>
    </table>
</body>
</html>
