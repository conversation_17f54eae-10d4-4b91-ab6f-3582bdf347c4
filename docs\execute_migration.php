<?php
/**
 * Script d'exécution de la migration vers le suivi historique de charge horaire
 * 
 * ATTENTION: Ce script modifie la structure de la base de données
 * Assurez-vous d'avoir une sauvegarde avant de l'exécuter
 */

require_once '../config/db.php';

// Configuration
$migrationFile = __DIR__ . '/migrate_workload_history.sql';
$logFile = __DIR__ . '/migration_log.txt';

echo "<h1>🔄 Migration vers le Suivi Historique de Charge Horaire</h1>";

// Vérifications préliminaires
echo "<h2>📋 Vérifications Préliminaires</h2>";

// Vérifier la connexion à la base de données
$conn = getConnection();
if (!$conn) {
    die("❌ <strong>ERREUR:</strong> Impossible de se connecter à la base de données<br>");
}
echo "✅ Connexion à la base de données établie<br>";

// Vérifier que le fichier de migration existe
if (!file_exists($migrationFile)) {
    die("❌ <strong>ERREUR:</strong> Fichier de migration non trouvé: $migrationFile<br>");
}
echo "✅ Fichier de migration trouvé<br>";

// Vérifier que la table enseignant existe
$result = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant'");
if (mysqli_num_rows($result) === 0) {
    die("❌ <strong>ERREUR:</strong> Table 'enseignant' non trouvée<br>");
}
echo "✅ Table 'enseignant' trouvée<br>";

// Vérifier s'il y a des données à migrer
$result = mysqli_query($conn, "SELECT COUNT(*) as count FROM enseignant");
$row = mysqli_fetch_assoc($result);
$teacherCount = $row['count'];
echo "✅ $teacherCount enseignants trouvés dans la base<br>";

// Vérifier si la migration a déjà été effectuée
$result = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_workload_history'");
$alreadyMigrated = mysqli_num_rows($result) > 0;

if ($alreadyMigrated) {
    echo "⚠️ <strong>ATTENTION:</strong> La table 'enseignant_workload_history' existe déjà<br>";
    echo "La migration semble avoir déjà été effectuée.<br>";
    
    // Vérifier les données existantes
    $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM enseignant_workload_history");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "📊 {$row['count']} enregistrements trouvés dans l'historique<br>";
    }
    
    echo "<br><strong>Voulez-vous continuer quand même ? (cela peut écraser les données existantes)</strong><br>";
    echo "<a href='?force=1' class='btn btn-warning'>⚠️ Forcer la migration</a> ";
    echo "<a href='?test=1' class='btn btn-info'>🧪 Mode test seulement</a><br><br>";
    
    if (!isset($_GET['force']) && !isset($_GET['test'])) {
        exit();
    }
}

echo "<hr>";

// Mode test
if (isset($_GET['test'])) {
    echo "<h2>🧪 Mode Test - Vérification des Requêtes</h2>";
    
    $sqlContent = file_get_contents($migrationFile);
    $statements = explode(';', $sqlContent);
    $validStatements = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (empty($statement) || strpos($statement, '--') === 0) continue;
        
        // Tester la syntaxe sans exécuter
        $testResult = mysqli_prepare($conn, $statement);
        if ($testResult) {
            $validStatements++;
            mysqli_stmt_close($testResult);
        } else {
            echo "❌ Erreur de syntaxe dans: " . substr($statement, 0, 100) . "...<br>";
            echo "Erreur: " . mysqli_error($conn) . "<br>";
        }
    }
    
    echo "✅ $validStatements requêtes SQL valides trouvées<br>";
    echo "<br><a href='?execute=1' class='btn btn-success'>🚀 Exécuter la migration</a><br>";
    exit();
}

// Exécution de la migration
if (isset($_GET['execute']) || isset($_GET['force'])) {
    echo "<h2>🚀 Exécution de la Migration</h2>";
    
    // Démarrer le log
    $logContent = "=== MIGRATION WORKLOAD HISTORY ===\n";
    $logContent .= "Date: " . date('Y-m-d H:i:s') . "\n";
    $logContent .= "Utilisateur: " . ($_SESSION['user']['username'] ?? 'unknown') . "\n\n";
    
    // Lire le fichier SQL
    $sqlContent = file_get_contents($migrationFile);
    
    // Séparer les requêtes
    $statements = explode(';', $sqlContent);
    
    $successCount = 0;
    $errorCount = 0;
    $skippedCount = 0;
    
    echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9;'>";
    
    foreach ($statements as $index => $statement) {
        $statement = trim($statement);
        
        // Ignorer les commentaires et les lignes vides
        if (empty($statement) || strpos($statement, '--') === 0) {
            $skippedCount++;
            continue;
        }
        
        echo "<strong>Requête " . ($index + 1) . ":</strong> " . substr($statement, 0, 80) . "...<br>";
        
        $result = mysqli_query($conn, $statement);
        
        if ($result) {
            echo "✅ <span style='color: green;'>Succès</span><br>";
            $successCount++;
            $logContent .= "SUCCESS: " . substr($statement, 0, 100) . "...\n";
        } else {
            echo "❌ <span style='color: red;'>Erreur: " . mysqli_error($conn) . "</span><br>";
            $errorCount++;
            $logContent .= "ERROR: " . mysqli_error($conn) . " - " . substr($statement, 0, 100) . "...\n";
        }
        
        echo "<br>";
        
        // Pause pour éviter les timeouts
        if ($index % 10 === 0) {
            flush();
            usleep(100000); // 0.1 seconde
        }
    }
    
    echo "</div>";
    
    // Résumé
    echo "<h3>📊 Résumé de la Migration</h3>";
    echo "<ul>";
    echo "<li>✅ <strong>Succès:</strong> $successCount requêtes</li>";
    echo "<li>❌ <strong>Erreurs:</strong> $errorCount requêtes</li>";
    echo "<li>⏭️ <strong>Ignorées:</strong> $skippedCount requêtes</li>";
    echo "</ul>";
    
    // Vérifications post-migration
    echo "<h3>🔍 Vérifications Post-Migration</h3>";
    
    // Vérifier la table
    $result = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_workload_history'");
    if (mysqli_num_rows($result) > 0) {
        echo "✅ Table 'enseignant_workload_history' créée<br>";
        
        // Compter les enregistrements
        $result = mysqli_query($conn, "SELECT COUNT(*) as count FROM enseignant_workload_history");
        if ($result) {
            $row = mysqli_fetch_assoc($result);
            echo "✅ {$row['count']} enregistrements dans l'historique<br>";
        }
    } else {
        echo "❌ Table 'enseignant_workload_history' non créée<br>";
    }
    
    // Vérifier la vue
    $result = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_current_workload'");
    if (mysqli_num_rows($result) > 0) {
        echo "✅ Vue 'enseignant_current_workload' créée<br>";
    } else {
        echo "❌ Vue 'enseignant_current_workload' non créée<br>";
    }
    
    // Vérifier la fonction
    $result = mysqli_query($conn, "SELECT getCurrentAcademicYear() as current_year");
    if ($result) {
        $row = mysqli_fetch_assoc($result);
        echo "✅ Fonction getCurrentAcademicYear() fonctionne: {$row['current_year']}<br>";
    } else {
        echo "❌ Fonction getCurrentAcademicYear() non fonctionnelle<br>";
    }
    
    // Sauvegarder le log
    $logContent .= "\n=== RÉSUMÉ ===\n";
    $logContent .= "Succès: $successCount\n";
    $logContent .= "Erreurs: $errorCount\n";
    $logContent .= "Ignorées: $skippedCount\n";
    $logContent .= "Fin: " . date('Y-m-d H:i:s') . "\n";
    
    file_put_contents($logFile, $logContent);
    echo "<br>📝 Log sauvegardé dans: $logFile<br>";
    
    if ($errorCount === 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
        echo "<h4 style='color: #155724;'>🎉 Migration Réussie !</h4>";
        echo "<p style='color: #155724;'>La migration vers le suivi historique de charge horaire a été effectuée avec succès.</p>";
        echo "<p><strong>Prochaines étapes :</strong></p>";
        echo "<ul>";
        echo "<li>Tester l'interface : <a href='../view/chef/workload_history.php' target='_blank'>Historique de Charge Horaire</a></li>";
        echo "<li>Vérifier les calculs de charge horaire</li>";
        echo "<li>Former les utilisateurs sur les nouvelles fonctionnalités</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24;'>⚠️ Migration Partiellement Réussie</h4>";
        echo "<p style='color: #721c24;'>La migration a rencontré $errorCount erreurs. Veuillez vérifier le log pour plus de détails.</p>";
        echo "</div>";
    }
    
} else {
    // Interface de démarrage
    echo "<h2>🚀 Prêt pour la Migration</h2>";
    echo "<p>Cette migration va :</p>";
    echo "<ul>";
    echo "<li>✅ Créer la table <code>enseignant_workload_history</code></li>";
    echo "<li>✅ Migrer les données existantes</li>";
    echo "<li>✅ Créer les vues et fonctions nécessaires</li>";
    echo "<li>✅ Configurer la synchronisation automatique</li>";
    echo "<li>✅ Préserver la compatibilité avec l'ancien système</li>";
    echo "</ul>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin: 20px 0;'>";
    echo "<h4>⚠️ Important</h4>";
    echo "<p>Assurez-vous d'avoir une sauvegarde de votre base de données avant de continuer.</p>";
    echo "</div>";
    
    echo "<br>";
    echo "<a href='?test=1' class='btn btn-info' style='padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🧪 Mode Test</a>";
    echo "<a href='?execute=1' class='btn btn-success' style='padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px;'>🚀 Exécuter la Migration</a>";
}

mysqli_close($conn);
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fc;
    margin: 20px;
    line-height: 1.6;
    color: #5a5c69;
}

h1, h2, h3, h4 {
    color: #5a5c69;
}

code {
    background-color: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #e83e8c;
    font-size: 0.9em;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    margin: 5px;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-warning {
    background-color: #ffc107;
    color: black;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

hr {
    border: none;
    border-top: 1px solid #dee2e6;
    margin: 20px 0;
}
</style>
