<?php
// Debug session state
session_start();

echo "=== SESSION DEBUG ===\n\n";

if (!isset($_SESSION['user'])) {
    echo "❌ No user session found\n";
    echo "Please log in first\n";
    exit;
}

echo "✅ User session found\n";
echo "Session data:\n";
print_r($_SESSION['user']);

// Check if teacher_id is set
if (isset($_SESSION['user']['teacher_id'])) {
    echo "\n✅ Teacher ID found: " . $_SESSION['user']['teacher_id'] . "\n";
} else {
    echo "\n❌ Teacher ID not found in session\n";
    
    // Try to get teacher info manually
    require_once 'model/authModel.php';
    
    $username = $_SESSION['user']['username'] ?? null;
    if ($username) {
        echo "Trying to get teacher info for username: $username\n";
        $teacherInfo = getTeacher($username);
        
        if ($teacherInfo) {
            echo "✅ Teacher info found:\n";
            print_r($teacherInfo);
        } else {
            echo "❌ No teacher info found for username: $username\n";
        }
    }
}

echo "\n=== END DEBUG ===\n";
?>
