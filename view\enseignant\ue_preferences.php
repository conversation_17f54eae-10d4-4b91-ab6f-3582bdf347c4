<?php
// Vérifier l'authentification
require_once '../includes/auth_check_enseignant.php';
require_once '../../model/uePreferencesModel.php';

// Get the current academic year
$academicYear = getCurrentAcademicYear();

// Get the teacher's ID from the session with fallback
$teacherId = $_SESSION['user']['teacher_id'] ?? null;

// If teacher_id is not set, try to get it from the database
if (!$teacherId) {
    require_once '../../model/authModel.php';
    $teacherInfo = getTeacher($_SESSION['user']['username']);
    if ($teacherInfo) {
        $teacherId = $teacherInfo['id_enseignant'];
        $_SESSION['user']['teacher_id'] = $teacherId;
        $_SESSION['user']['specialty_id'] = $teacherInfo['id_specialite'];
        $_SESSION['user']['department_id'] = $teacherInfo['id_departement'];
        $_SESSION['user']['specialty_name'] = $teacherInfo['specialty_name'];
        $_SESSION['user']['department_name'] = $teacherInfo['department_name'];
    } else {
        // If still no teacher found, show error
        die('Erreur: Informations enseignant non trouvées. Veuillez contacter l\'administrateur.');
    }
}
// Get teacher's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$teacherName = trim($prenom . ' ' . $nom);
if (empty($teacherName)) {
    $teacherName = $_SESSION['user']['username'] ?? 'Enseignant';
}
$specialtyName = $_SESSION['user']['specialty_name'];

// Page title
$pageTitle = "Préférences d'Unités d'Enseignement";
$currentPage = "ue_preferences.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        :root {
            --primary-color: #4e73df;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --dark-color: #5a5c69;
            --light-color: #f8f9fc;
            --card-border-radius: 0.5rem;
            --transition-speed: 0.3s;
        }

        /* Page Layout */
        .main-content {
            background-color: #f8f9fc;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            margin-bottom: 1.5rem;
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
        }

        .card:hover {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #e3e6f0;
            background-color: white;
            border-top-left-radius: var(--card-border-radius) !important;
            border-top-right-radius: var(--card-border-radius) !important;
        }

        /* Module Cards */
        .module-card {
            transition: all var(--transition-speed) ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .module-card.selected {
            border-left: 4px solid var(--primary-color);
            background-color: var(--primary-light);
        }

        .module-card .card-body {
            padding: 1rem;
        }

        .module-card .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        /* UE Cards */
        .ue-card {
            transition: all var(--transition-speed) ease;
            border-left: 4px solid transparent;
            margin-bottom: 0.5rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.05);
            padding: 0.75rem !important;
        }

        .ue-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
        }

        .ue-card.selected {
            border-left: 4px solid var(--primary-color);
            background-color: var(--primary-light);
        }

        .ue-type {
            font-weight: 600;
            display: inline-block;
            padding: 0.15rem 0.4rem;
            border-radius: 0.25rem;
            margin-right: 0.4rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ue-type.cours {
            background-color: rgba(78, 115, 223, 0.1);
            color: var(--primary-color);
        }

        .ue-type.td {
            background-color: rgba(28, 200, 138, 0.1);
            color: var(--secondary-color);
        }

        .ue-type.tp {
            background-color: rgba(246, 194, 62, 0.1);
            color: var(--warning-color);
        }

        .preference-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 0.8rem;
            border-radius: 50px;
            padding: 0.25rem 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(78, 115, 223, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0);
            }
        }

        .module-info {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 1rem;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }

        .module-info-item {
            display: flex;
            align-items: center;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .module-info-item:not(:last-child)::after {
            content: "•";
            margin-left: 1rem;
            color: #d1d1d1;
        }

        .module-info i {
            width: 16px;
            margin-right: 0.25rem;
            color: var(--primary-color);
        }

        /* Preference List */
        .preference-list {
            max-height: 500px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .preference-list::-webkit-scrollbar {
            width: 6px;
        }

        .preference-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .preference-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .preference-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .preference-item {
            padding: 1rem;
            border-left: 4px solid var(--primary-color);
            margin-bottom: 0.75rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.05);
            transition: all var(--transition-speed) ease;
        }

        .preference-item:hover {
            transform: translateX(5px);
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
        }

        .preference-item h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .preference-level {
            font-weight: bold;
            color: var(--primary-color);
            display: inline-block;
            padding: 0.2rem 0.5rem;
            background-color: var(--primary-light);
            border-radius: 0.25rem;
            margin-right: 0.5rem;
        }

        /* Alert Styles */
        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }

        .alert {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: none;
            border-left: 4px solid;
            border-radius: 0.25rem;
            padding: 1rem;
            margin-bottom: 1rem;
            animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes slideIn {
            0% {
                transform: translateX(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert.fade {
            animation: slideOut 0.3s ease-in forwards;
        }

        @keyframes slideOut {
            0% {
                transform: translateX(0);
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Search Bar */
        .search-container {
            position: relative;
            max-width: 300px;
        }

        .search-container .form-control {
            padding-left: 2.5rem;
            border-radius: 50px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }

        .search-container .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
            border-color: #bac8f3;
        }

        .search-container .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #b7b9cc;
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--card-border-radius);
            border-top-right-radius: var(--card-border-radius);
        }

        .modal-header .btn-close {
            color: white;
            filter: brightness(0) invert(1);
        }

        /* Preference Floating Button */
        .preference-float-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1030;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Animation plus fluide */
        }

        .preference-float-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            background-color: #3a5fc8; /* Slightly darker shade of primary color */
        }

        .preference-float-btn:active {
            transform: translateY(-2px) scale(0.95);
            transition: all 0.1s ease;
        }

        .preference-float-btn i {
            font-size: 1.8rem;
        }

        .preference-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #e74a3b; /* Danger/alert color */
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }

        @keyframes badgePulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .badge-pulse {
            animation: badgePulse 0.5s ease-in-out;
        }

        /* Preferences Modal */
        .preferences-modal .modal-dialog {
            max-width: 700px;
        }

        .preferences-modal .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }

        .preferences-modal .preference-item {
            border-left: 4px solid var(--primary-color);
            background-color: white;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .preferences-modal .preference-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .preferences-modal .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .preferences-modal .empty-state i {
            font-size: 3rem;
            color: #d1d1d1;
            margin-bottom: 1rem;
        }

        /* Workload Progress Bar Styles */
        .workload-progress-card {
            border-left: 4px solid var(--primary-color);
            background: linear-gradient(135deg, rgba(78, 115, 223, 0.05), rgba(78, 115, 223, 0.02));
        }

        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
            background-size: 300% 100%;
            transition: all 0.3s ease;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .progress-bar.insufficient {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
            animation: pulse-red 2s infinite;
        }

        .progress-bar.approaching {
            background: linear-gradient(90deg, #ffc107, #f39c12);
            animation: pulse-yellow 2s infinite;
        }

        .progress-bar.sufficient {
            background: linear-gradient(90deg, #28a745, #20c997);
            animation: pulse-green 2s infinite;
        }

        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 5px rgba(220, 53, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.8); }
        }

        @keyframes pulse-yellow {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8); }
        }

        #workloadStatus.insufficient {
            background-color: var(--danger-color) !important;
        }

        #workloadStatus.approaching {
            background-color: var(--warning-color) !important;
        }

        #workloadStatus.sufficient {
            background-color: var(--secondary-color) !important;
        }

        #workloadMessage.alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #721c24;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        #workloadMessage.alert-warning {
            background-color: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.2);
            color: #856404;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        #workloadMessage.alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            color: #155724;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        /* Submit button states */
        #submitPreferencesBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #submitPreferencesBtn.insufficient {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        #submitPreferencesBtn.sufficient {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* Simplified UI Improvements */

        /* Larger, easier to click checkboxes */
        .form-check-input {
            width: 1.2rem !important;
            height: 1.2rem !important;
            margin-top: 0.1rem;
            cursor: pointer;
        }

        .form-check-input:checked {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .form-check-input:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.15rem rgba(28, 200, 138, 0.25);
        }

        /* Simplified module cards with reduced clutter */
        .module-card {
            margin-bottom: 1rem;
            border: 1px solid #e3e6f0;
            border-radius: 8px;
            transition: all 0.2s ease;
        }

        .module-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            border-color: var(--primary-color);
        }

        .module-card .card-body {
            padding: 1rem;
        }

        .module-card .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: #2c3e50;
        }

        /* Simplified module info with better spacing */
        .module-info {
            background-color: #f8f9fa;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            margin-bottom: 0.75rem;
            font-size: 0.8rem;
            color: #6c757d;
        }

        .module-info-item {
            margin-right: 1rem;
            margin-bottom: 0.25rem;
        }

        .module-info-item:not(:last-child)::after {
            content: "•";
            margin-left: 0.75rem;
            color: #d1d1d1;
        }

        /* Simplified UE cards with better visual hierarchy */
        .ue-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            transition: all 0.2s ease;
            cursor: pointer;
            position: relative;
        }

        .ue-card:hover {
            border-color: var(--primary-color);
            background-color: #f8f9ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(78, 115, 223, 0.15);
        }

        .ue-card.selected {
            border-color: var(--secondary-color);
            background-color: #f0f9f0;
            border-width: 2px;
            box-shadow: 0 2px 8px rgba(28, 200, 138, 0.2);
        }

        .ue-card.selected::before {
            content: "✓";
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            background: var(--secondary-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: bold;
        }

        /* Clearer UE type badges */
        .ue-type {
            padding: 0.25rem 0.6rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            margin-right: 0.5rem;
        }

        /* Simplified floating button */
        .preference-float-btn {
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color), #5a6fd8);
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
            transition: all 0.2s ease;
        }

        .preference-float-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 16px rgba(78, 115, 223, 0.4);
        }

        .preference-float-btn i {
            font-size: 1.5rem;
        }

        /* More prominent badge */
        .preference-badge {
            width: 22px;
            height: 22px;
            background-color: #e74a3b;
            color: white;
            border-radius: 50%;
            font-size: 0.75rem;
            font-weight: 700;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        /* Simplified search bar */
        .search-container {
            max-width: 280px;
        }

        .search-container .form-control {
            padding: 0.5rem 0.75rem 0.5rem 2.25rem;
            border-radius: 20px;
            border: 1px solid #e3e6f0;
            background-color: white;
            font-size: 0.9rem;
        }

        .search-container .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.15rem rgba(78, 115, 223, 0.15);
        }

        .search-container .search-icon {
            left: 0.75rem;
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* Simplified buttons */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #3d5bd0;
            border-color: #3d5bd0;
            transform: translateY(-1px);
        }

        .btn-lg {
            padding: 0.75rem 2rem;
            font-size: 1rem;
        }

        /* Select All Button Styling */
        .select-all-btn {
            font-size: 0.8rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            transition: all 0.2s ease;
        }

        .select-all-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(78, 115, 223, 0.2);
        }

        .select-all-btn.all-selected {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .select-all-btn.all-selected:hover {
            background-color: #1e7e34;
            border-color: #1e7e34;
            color: white;
        }

        .select-all-btn.btn-success {
            background-color: var(--secondary-color) !important;
            border-color: var(--secondary-color) !important;
        }

        .select-all-btn.btn-success:hover {
            background-color: #1e7e34 !important;
            border-color: #1e7e34 !important;
        }

        /* Simplified modal */
        .modal-content {
            border-radius: 8px;
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), #5a6fd8);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 1rem 1.25rem;
        }

        .modal-body {
            padding: 1.25rem;
        }

        /* Simplified preference items in modal */
        .preference-item {
            background: white;
            border: 1px solid #e9ecef;
            border-left: 4px solid var(--primary-color);
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            transition: all 0.2s ease;
        }

        .preference-item:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transform: translateY(-1px);
        }

        .preference-item h6 {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .card-header {
                flex-direction: column;
                align-items: flex-start !important;
                padding: 0.75rem 1rem;
            }

            .search-container {
                max-width: 100%;
                width: 100%;
                margin-top: 0.75rem;
            }

            .workload-progress-card .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .workload-progress-card .d-flex > * {
                margin-bottom: 0.5rem;
            }

            .module-card .card-body {
                padding: 0.75rem;
            }

            .preference-float-btn {
                width: 50px;
                height: 50px;
                bottom: 20px;
                right: 20px;
            }

            .preference-float-btn i {
                font-size: 1.3rem;
            }

            .btn-lg {
                padding: 0.6rem 1.5rem;
                font-size: 0.95rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Header standard -->
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-start flex-wrap">
                            <div class="mb-3 mb-md-0">
                                <h1 class="page-title mb-2"><?php echo $pageTitle; ?></h1>
                                <p class="text-muted mb-1">Sélectionnez vos préférences d'enseignement pour l'année académique <?php echo $academicYear; ?></p>
                                <span class="badge bg-primary rounded-pill py-1 px-3">
                                    <i class="bi bi-mortarboard-fill me-1"></i> <?php echo htmlspecialchars($specialtyName); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Workload Progress Bar -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card workload-progress-card">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="mb-0 fw-bold text-primary">
                                        <i class="bi bi-speedometer2 me-2"></i>Charge de Travail
                                    </h6>
                                    <span class="badge bg-secondary" id="workloadStatus">Chargement...</span>
                                </div>

                                <div class="progress mb-3" style="height: 18px;">
                                    <div class="progress-bar" id="workloadProgressBar" role="progressbar"
                                         style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        <span id="progressPercentage" class="fw-bold">0%</span>
                                    </div>
                                </div>

                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="small text-muted">Sélectionnées</div>
                                        <div class="fw-bold text-primary"><span id="selectedHours">0</span>h</div>
                                    </div>
                                    <div class="col-6">
                                        <div class="small text-muted">Minimum requis</div>
                                        <div class="fw-bold text-secondary"><span id="minimumHours">--</span>h</div>
                                    </div>
                                </div>

                                <div id="workloadMessage" class="mt-2 text-center small" style="display: none;">
                                    <!-- Dynamic message will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 text-primary fw-bold">
                                    <i class="bi bi-book me-2"></i>Modules Disponibles
                                </h5>
                                <div class="search-container">
                                    <i class="bi bi-search search-icon"></i>
                                    <input type="text" class="form-control" id="moduleSearch" placeholder="Rechercher...">
                                </div>
                            </div>
                            <div class="card-body p-3">
                                <div class="row g-3" id="modulesContainer">
                                    <!-- Modules will be loaded here dynamically -->
                                    <div class="col-12 text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                        <p class="mt-3 text-muted">Chargement des modules...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button id="submitPreferencesBtn" class="btn btn-primary btn-lg shadow-sm">
                            <i class="bi bi-check-circle me-2"></i>Valider mes Préférences
                        </button>
                        <p class="small text-muted mt-2 mb-0">
                            Vos préférences seront enregistrées et transmises au département
                        </p>
                    </div>
                </div>
            </div>

            <!-- Floating Preferences Button -->
            <div class="preference-float-btn" id="preferenceFloatBtn" data-bs-toggle="modal" data-bs-target="#preferencesListModal"
                 data-bs-title="Voir mes sélections" data-bs-placement="left">
                <i class="bi bi-heart-fill"></i>
                <span class="preference-badge" id="preferenceCartBadge">0</span>
            </div>
        </div>
    </div>

    <!-- UE Preference Modal -->
    <div class="modal fade" id="preferenceModal" tabindex="-1" aria-labelledby="preferenceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="preferenceModalLabel">
                        <i class="bi bi-heart me-2"></i>Ajouter une Préférence
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="preferenceForm">
                        <input type="hidden" id="ueId" name="ueId">
                        <input type="hidden" id="moduleName" name="moduleName">
                        <input type="hidden" id="ueType" name="ueType">

                        <div class="mb-4">
                            <div class="card border-primary">
                                <div class="card-body p-3">
                                    <h6 class="fw-bold text-primary mb-2">
                                        <i class="bi bi-book me-1"></i><span id="moduleNameDisplay"></span>
                                    </h6>
                                    <div class="d-flex align-items-center gap-3">
                                        <span id="ueTypeDisplay" class="badge bg-primary"></span>
                                        <span class="text-muted">
                                            <i class="bi bi-clock me-1"></i><span id="ueVolumeDisplay"></span> heures
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="preferenceReason" class="form-label fw-bold">
                                <i class="bi bi-chat-text me-1"></i>Commentaire (optionnel)
                            </label>
                            <textarea class="form-control" id="preferenceReason" name="preferenceReason" rows="3"
                                placeholder="Pourquoi souhaitez-vous enseigner cette unité ?"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="savePreferenceBtn">
                        <i class="bi bi-heart-fill me-1"></i>Ajouter à mes Préférences
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Preferences List Modal -->
    <div class="modal fade preferences-modal" id="preferencesListModal" tabindex="-1" aria-labelledby="preferencesListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="preferencesListModalLabel">
                        <i class="bi bi-heart-fill me-2"></i>Mes Préférences d'Enseignement
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="preferencesModalContainer">
                        <!-- Preferences will be loaded here dynamically -->
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-3 text-muted">Chargement de vos préférences...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        Fermer
                    </button>
                    <button type="button" class="btn btn-primary" id="submitModalPreferencesBtn" data-bs-dismiss="modal">
                        <i class="bi bi-check-circle me-1"></i>Valider mes Préférences
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Global variables
        const teacherId = <?php echo $teacherId ?: 'null'; ?>;
        const academicYear = '<?php echo $academicYear; ?>';
        const teacherRole = '<?php echo $_SESSION['user']['role'] ?? 'enseignant'; ?>'; // Get teacher role from session

        // Check if teacherId is valid
        if (!teacherId) {
            console.error('Teacher ID not found');
            document.getElementById('modulesContainer').innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                    <h5 class="text-warning mt-3">Erreur de Configuration</h5>
                    <p class="text-muted">Impossible de charger vos informations d'enseignant. Veuillez vous reconnecter.</p>
                    <a href="../../index.php" class="btn btn-primary">Se Reconnecter</a>
                </div>`;
        }
        let modules = [];
        let preferences = [];
        let selectedUEs = new Set(); // Pour stocker les UEs sélectionnées
        let minimumWorkloadHours = 0; // Minimum required hours
        let selectedWorkloadHours = 0; // Currently selected hours

        // DOM elements
        const modulesContainer = document.getElementById('modulesContainer');
        const preferencesContainer = document.getElementById('preferencesModalContainer');
        const moduleSearch = document.getElementById('moduleSearch');
        const preferenceModal = new bootstrap.Modal(document.getElementById('preferenceModal'));
        const preferencesListModal = new bootstrap.Modal(document.getElementById('preferencesListModal'));
        const preferenceForm = document.getElementById('preferenceForm');
        const ueIdInput = document.getElementById('ueId');
        const moduleNameInput = document.getElementById('moduleName');
        const ueTypeInput = document.getElementById('ueType');
        const moduleNameDisplay = document.getElementById('moduleNameDisplay');
        const ueTypeDisplay = document.getElementById('ueTypeDisplay');
        const ueVolumeDisplay = document.getElementById('ueVolumeDisplay');
        const preferenceReasonInput = document.getElementById('preferenceReason');
        const savePreferenceBtn = document.getElementById('savePreferenceBtn');
        const submitPreferencesBtn = document.getElementById('submitPreferencesBtn');
        const submitModalPreferencesBtn = document.getElementById('submitModalPreferencesBtn');
        const preferenceCartBadge = document.getElementById('preferenceCartBadge');

        // Progress bar elements will be accessed dynamically to avoid null reference errors

        // Load modules and preferences when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            loadMinimumWorkload();
            loadModulesAndUEs();
            loadPreferences();

            // Add event listeners
            moduleSearch.addEventListener('input', filterModules);
            savePreferenceBtn.addEventListener('click', savePreference);
            submitPreferencesBtn.addEventListener('click', submitAllPreferences);
            submitModalPreferencesBtn.addEventListener('click', submitAllPreferences);

            // Délégation d'événements pour les cases à cocher
            document.addEventListener('change', function(e) {
                if (e.target && e.target.classList.contains('ue-checkbox')) {
                    handleCheckboxChange(e.target);
                }
            });

            // Initialiser le badge du panier
            updatePreferenceCartBadge();

            // Initialiser les tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Ajouter un événement pour ouvrir le modal des préférences
            document.getElementById('preferenceFloatBtn').addEventListener('click', function() {
                renderPreferencesInModal();
            });
        });

        // Load minimum workload configuration for the teacher
        async function loadMinimumWorkload() {
            try {
                const url = `../../controller/configurationChargeController.php?action=getMinimumWorkloadForTeacher&role=${teacherRole}&academic_year=${academicYear}`;

                const response = await fetch(url);

                if (!response.ok) {
                    // Set default minimum if no configuration found
                    minimumWorkloadHours = 0;
                    updateWorkloadProgress();
                    return;
                }

                const result = await response.json();

                if (result.error) {
                    // Set default minimum if no configuration found
                    minimumWorkloadHours = 0;
                } else {
                    minimumWorkloadHours = parseInt(result.data.charge_minimale) || 0;
                }

                updateWorkloadProgress();
            } catch (error) {
                console.error('Error loading minimum workload:', error);
                minimumWorkloadHours = 0;
                updateWorkloadProgress();
            }
        }

        // Update workload progress bar
        function updateWorkloadProgress() {
            // Check if progress bar elements exist
            const workloadProgressBar = document.getElementById('workloadProgressBar');
            const selectedHours = document.getElementById('selectedHours');
            const minimumHours = document.getElementById('minimumHours');
            const progressPercentage = document.getElementById('progressPercentage');
            const workloadStatus = document.getElementById('workloadStatus');
            const workloadMessage = document.getElementById('workloadMessage');

            if (!workloadProgressBar || !selectedHours || !minimumHours || !progressPercentage) {
                console.warn('Some workload progress elements not found, skipping update');
                return;
            }

            // Calculate selected hours from checked UEs
            selectedWorkloadHours = 0;
            const checkedBoxes = document.querySelectorAll('.ue-checkbox:checked');

            checkedBoxes.forEach(checkbox => {
                const ueVolume = parseInt(checkbox.dataset.ueVolume) || 0;
                selectedWorkloadHours += ueVolume;
            });

            // Update display elements
            selectedHours.textContent = selectedWorkloadHours;
            minimumHours.textContent = minimumWorkloadHours || '--';

            // Calculate progress percentage
            let progressPercent = 0;
            if (minimumWorkloadHours > 0) {
                progressPercent = Math.min((selectedWorkloadHours / minimumWorkloadHours) * 100, 100);
            }

            // Update progress bar
            workloadProgressBar.style.width = progressPercent + '%';
            workloadProgressBar.setAttribute('aria-valuenow', progressPercent);
            progressPercentage.textContent = Math.round(progressPercent) + '%';

            // Update status and styling based on progress
            updateWorkloadStatus(progressPercent, workloadStatus, workloadMessage);
        }

        // Update workload status and styling
        function updateWorkloadStatus(progressPercent, workloadStatus, workloadMessage) {
            // Get elements if not passed as parameters
            const workloadProgressBar = document.getElementById('workloadProgressBar');
            const submitPreferencesBtn = document.getElementById('submitPreferencesBtn');
            const submitModalPreferencesBtn = document.getElementById('submitModalPreferencesBtn');

            // Calculate remaining hours
            const remaining = Math.max(minimumWorkloadHours - selectedWorkloadHours, 0);

            // Remove existing classes if elements exist
            if (workloadProgressBar) {
                workloadProgressBar.classList.remove('insufficient', 'approaching', 'sufficient');
            }
            if (workloadStatus) {
                workloadStatus.classList.remove('insufficient', 'approaching', 'sufficient');
            }
            if (submitPreferencesBtn) {
                submitPreferencesBtn.classList.remove('insufficient', 'sufficient');
            }
            if (workloadMessage) {
                workloadMessage.classList.remove('alert-danger', 'alert-warning', 'alert-success');
            }

            let statusText = '';
            let messageText = '';
            let messageClass = '';
            let shouldDisableSubmit = false;

            if (minimumWorkloadHours === 0) {
                // No minimum configuration found
                statusText = 'Non configuré';
                messageText = 'Aucune configuration de charge minimale trouvée pour votre rôle.';
                messageClass = 'alert-warning';
                if (workloadStatus) {
                    workloadStatus.className = 'badge bg-secondary';
                }
                shouldDisableSubmit = selectedUEs.size === 0;
            } else if (progressPercent < 80) {
                // Insufficient (less than 80% of minimum)
                statusText = 'Insuffisant';
                messageText = `Vous devez sélectionner au moins ${minimumWorkloadHours} heures. Il vous reste ${remaining} heures à sélectionner.`;
                messageClass = 'alert-danger';
                if (workloadProgressBar) workloadProgressBar.classList.add('insufficient');
                if (workloadStatus) workloadStatus.classList.add('insufficient');
                if (submitPreferencesBtn) submitPreferencesBtn.classList.add('insufficient');
                shouldDisableSubmit = true;
            } else if (progressPercent < 100) {
                // Approaching (80-99% of minimum)
                statusText = 'Proche';
                messageText = `Vous êtes proche de l'objectif. Il vous reste ${remaining} heures à sélectionner.`;
                messageClass = 'alert-warning';
                if (workloadProgressBar) workloadProgressBar.classList.add('approaching');
                if (workloadStatus) workloadStatus.classList.add('approaching');
                shouldDisableSubmit = true;
            } else {
                // Sufficient (100% or more of minimum)
                statusText = 'Suffisant';
                messageText = 'Félicitations ! Vous avez atteint la charge minimale requise.';
                messageClass = 'alert-success';
                if (workloadProgressBar) workloadProgressBar.classList.add('sufficient');
                if (workloadStatus) workloadStatus.classList.add('sufficient');
                if (submitPreferencesBtn) submitPreferencesBtn.classList.add('sufficient');
                shouldDisableSubmit = false;
            }

            // Update submit button states
            if (submitPreferencesBtn) {
                submitPreferencesBtn.disabled = shouldDisableSubmit;
            }
            if (submitModalPreferencesBtn) {
                submitModalPreferencesBtn.disabled = shouldDisableSubmit;
            }

            // Update status badge
            if (workloadStatus) {
                workloadStatus.textContent = statusText;
            }

            // Update message
            if (workloadMessage) {
                if (messageText) {
                    workloadMessage.textContent = messageText;
                    workloadMessage.className = `mt-2 text-center small ${messageClass}`;
                    workloadMessage.style.display = 'block';
                } else {
                    workloadMessage.style.display = 'none';
                }
            }
        }

        // Gérer le changement d'état des cases à cocher
        function handleCheckboxChange(checkbox) {
            const ueId = parseInt(checkbox.value);

            if (checkbox.checked) {
                selectedUEs.add(ueId);
            } else {
                selectedUEs.delete(ueId);
            }

            // Mettre à jour l'interface
            updateSelectedCount();
            updateWorkloadProgress(); // Update progress bar
            updateSelectAllButtons(); // Update select-all button states
        }

        // Handle select all toggle for a specific module
        function handleSelectAllToggle(button) {
            const moduleId = button.dataset.moduleId;
            const isCurrentlySelected = button.dataset.selected === 'true';

            // Find all UE checkboxes for this module
            const moduleCheckboxes = document.querySelectorAll(`.ue-checkbox[data-module-id="${moduleId}"]`);

            // Toggle all checkboxes in this module
            moduleCheckboxes.forEach(checkbox => {
                const ueId = parseInt(checkbox.value);

                if (isCurrentlySelected) {
                    // Deselect all
                    checkbox.checked = false;
                    selectedUEs.delete(ueId);
                } else {
                    // Select all
                    checkbox.checked = true;
                    selectedUEs.add(ueId);
                }

                // Update visual state of the UE card
                const ueCard = checkbox.closest('.ue-card');
                if (ueCard) {
                    if (checkbox.checked) {
                        ueCard.classList.add('selected');
                    } else {
                        ueCard.classList.remove('selected');
                    }
                }
            });

            // Update the interface
            updateSelectedCount();
            updateWorkloadProgress();
            updateSelectAllButtons();
        }

        // Update the state and text of select-all buttons
        function updateSelectAllButtons() {
            document.querySelectorAll('.select-all-btn').forEach(button => {
                const moduleId = button.dataset.moduleId;
                const moduleCheckboxes = document.querySelectorAll(`.ue-checkbox[data-module-id="${moduleId}"]`);
                const checkedCheckboxes = document.querySelectorAll(`.ue-checkbox[data-module-id="${moduleId}"]:checked`);

                const allSelected = moduleCheckboxes.length > 0 && moduleCheckboxes.length === checkedCheckboxes.length;

                // Update button state
                button.dataset.selected = allSelected.toString();

                if (allSelected) {
                    button.classList.remove('btn-outline-primary');
                    button.classList.add('btn-success', 'all-selected');
                    button.innerHTML = '<i class="bi bi-check-all me-1"></i>Désélectionner tout';
                } else {
                    button.classList.remove('btn-success', 'all-selected');
                    button.classList.add('btn-outline-primary');
                    button.innerHTML = '<i class="bi bi-check-all me-1"></i>Sélectionner tout';
                }
            });
        }

        // Mettre à jour le compteur de sélection
        function updateSelectedCount() {
            const count = selectedUEs.size;
            const baseText = count > 0 ? `Valider mes Préférences (${count})` : 'Valider mes Préférences';
            submitPreferencesBtn.innerHTML = `<i class="bi bi-check-circle me-2"></i>${baseText}`;
            submitModalPreferencesBtn.innerHTML = `<i class="bi bi-check-circle me-1"></i>${baseText}`;

            // Don't override the disabled state here - let updateWorkloadStatus handle it
            // Only disable if no UEs are selected AND no minimum workload requirement
            if (count === 0 && minimumWorkloadHours === 0) {
                submitPreferencesBtn.disabled = true;
                submitModalPreferencesBtn.disabled = true;
            }
            // If there's a minimum workload requirement, let updateWorkloadStatus control the button state

            // Mettre à jour le badge du panier
            updatePreferenceCartBadge();
        }

        // Mettre à jour le badge du bouton flottant de préférences
        function updatePreferenceCartBadge() {
            const count = selectedUEs.size;
            preferenceCartBadge.textContent = count;

            if (count > 0) {
                preferenceCartBadge.style.display = 'flex';
                // Ajouter une animation subtile pour attirer l'attention
                preferenceCartBadge.classList.add('badge-pulse');
                setTimeout(() => {
                    preferenceCartBadge.classList.remove('badge-pulse');
                }, 1000);
            } else {
                preferenceCartBadge.style.display = 'none';
            }
        }

        // Load modules and their teaching units matching the teacher's specialty
        async function loadModulesAndUEs() {
            try {
                const url = `../../route/uePreferencesRoute.php?action=getModulesAndUEsByTeacherSpecialty&teacher_id=${teacherId}`;
                console.log('Fetching modules and UEs from:', url);

                const response = await fetch(url);

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Server error: ${response.status}</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Error parsing server response</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">${result.error}</p></div>`;
                    return;
                }

                modules = result.data;
                renderModules();
            } catch (error) {
                console.error('Error loading modules and UEs:', error);
                showAlert('Erreur lors du chargement des modules et UEs. Veuillez réessayer.', 'danger');
                modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Erreur lors du chargement des modules et UEs. Veuillez réessayer.</p></div>`;
            }
        }

        // Load the teacher's UE preferences
        async function loadPreferences() {
            try {
                const url = `../../route/uePreferencesRoute.php?action=getTeacherUePreferences&teacher_id=${teacherId}`;
                console.log('Fetching preferences from:', url);

                const response = await fetch(url);

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Server error: ${response.status}</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Error parsing server response</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">${result.error}</p></div>`;
                    return;
                }

                preferences = result.data;
                renderPreferences();
                updateUECards();
            } catch (error) {
                console.error('Error loading preferences:', error);
                showAlert('Erreur lors du chargement des préférences. Veuillez réessayer.', 'danger');
                preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Erreur lors du chargement des préférences. Veuillez réessayer.</p></div>`;
            }
        }

        // Render modules and their teaching units in the modules container
        function renderModules() {
            if (modules.length === 0) {
                modulesContainer.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">Aucun module trouvé</h5>
                        <p class="text-muted">Aucun module correspondant à votre spécialité n'a été trouvé.</p>
                    </div>`;
                return;
            }

            let html = '';

            modules.forEach(module => {
                // Check if the module has teaching units
                if (!module.teaching_units || module.teaching_units.length === 0) {
                    return; // Skip modules without teaching units
                }

                html += `
                <div class="col-12 module-item" data-module-id="${module.id}" data-module-name="${module.nom}">
                    <div class="card module-card">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="bi bi-book text-primary me-2"></i>${module.nom}
                            </h5>
                            <div class="module-info">
                                <div class="module-info-item">
                                    <i class="bi bi-building"></i> ${module.nom_filiere || 'N/A'}
                                </div>
                                <div class="module-info-item">
                                    <i class="bi bi-layers"></i> ${module.niveau || 'N/A'}
                                </div>
                                <div class="module-info-item">
                                    <i class="bi bi-calendar3"></i> ${module.semestre || 'N/A'}
                                </div>
                                <div class="module-info-item">
                                    <i class="bi bi-clock"></i> ${module.volume_total || 'N/A'} heures
                                </div>
                            </div>
                            <div class="ue-list">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0 fw-bold text-secondary">Unités d'Enseignement</h6>
                                    <button class="btn btn-sm btn-outline-primary select-all-btn"
                                            data-module-id="${module.id}"
                                            data-selected="false">
                                        <i class="bi bi-check-all me-1"></i>Sélectionner tout
                                    </button>
                                </div>
                                <div class="row g-2">
                `;

                // Add teaching units for this module
                module.teaching_units.forEach(ue => {
                    const ueTypeClass = ue.type === 'Cours' ? 'cours' : (ue.type === 'TD' ? 'td' : 'tp');

                    html += `
                    <div class="col-12">
                        <div class="ue-card ue-item" data-ue-id="${ue.id}" data-module-id="${module.id}"
                             data-module-name="${module.nom}" data-ue-type="${ue.type}" data-ue-volume="${ue.volume_horaire}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center flex-grow-1">
                                    <div class="form-check me-3">
                                        <input class="form-check-input ue-checkbox" type="checkbox" value="${ue.id}"
                                            id="ue-${ue.id}" data-ue-id="${ue.id}" data-module-id="${module.id}"
                                            data-module-name="${module.nom}" data-ue-type="${ue.type}" data-ue-volume="${ue.volume_horaire}">
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="ue-type ${ueTypeClass}">${ue.type}</span>
                                            <span class="fw-bold text-primary">${ue.volume_horaire}h</span>
                                            ${ue.nb_groupes > 1 ? `<span class="badge bg-light text-dark">${ue.nb_groupes} groupes</span>` : ''}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
                });

                html += `
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            });

            modulesContainer.innerHTML = html;

            // Add click handlers to UE cards for easier selection
            document.querySelectorAll('.ue-card').forEach(card => {
                card.addEventListener('click', (e) => {
                    // Don't trigger if clicking directly on checkbox
                    if (e.target.classList.contains('ue-checkbox')) return;

                    const checkbox = card.querySelector('.ue-checkbox');
                    if (checkbox) {
                        checkbox.checked = !checkbox.checked;
                        handleCheckboxChange(checkbox);
                    }
                });
            });

            // Add click handlers to select-all buttons
            document.querySelectorAll('.select-all-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSelectAllToggle(button);
                });
            });

            // Update UE cards to show selected preferences
            updateUECards();

            // Update progress bar after modules are rendered
            updateWorkloadProgress();

            // Update select-all button states
            updateSelectAllButtons();
        }

        // Render preferences in the modal
        function renderPreferencesInModal() {
            if (preferences.length === 0) {
                preferencesContainer.innerHTML = `
                <div class="empty-state text-center py-5">
                    <i class="bi bi-heart text-muted" style="font-size: 4rem;"></i>
                    <h5 class="text-muted mt-3">Aucune préférence sélectionnée</h5>
                    <p class="text-muted">Commencez par sélectionner des unités d'enseignement qui vous intéressent.</p>
                    <button class="btn btn-primary mt-2" data-bs-dismiss="modal">
                        <i class="bi bi-plus-circle me-1"></i>Ajouter des Préférences
                    </button>
                </div>`;
                return;
            }

            // Sort preferences by module name and UE type
            preferences.sort((a, b) => {
                if (a.module_name !== b.module_name) {
                    return a.module_name.localeCompare(b.module_name);
                }
                return a.ue_type.localeCompare(b.ue_type);
            });

            let html = `
                <div class="mb-3 p-3 bg-light rounded">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="fw-bold text-primary">${preferences.length}</div>
                            <div class="small text-muted">Préférences</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-success">${preferences.reduce((sum, pref) => sum + parseInt(pref.volume_horaire || 0), 0)}h</div>
                            <div class="small text-muted">Volume total</div>
                        </div>
                        <div class="col-4">
                            <div class="fw-bold text-info">${new Set(preferences.map(p => p.module_name)).size}</div>
                            <div class="small text-muted">Modules</div>
                        </div>
                    </div>
                </div>
            `;

            preferences.forEach(pref => {
                const ueTypeClass = pref.ue_type === 'Cours' ? 'cours' : (pref.ue_type === 'TD' ? 'td' : 'tp');

                html += `
                <div class="preference-item mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-2 text-primary">
                                <i class="bi bi-book me-1"></i>${pref.module_name}
                            </h6>
                            <div class="d-flex align-items-center gap-2 mb-2">
                                <span class="ue-type ${ueTypeClass}">${pref.ue_type}</span>
                                <span class="fw-bold text-primary">${pref.volume_horaire}h</span>
                            </div>
                            ${pref.reason ? `<div class="small text-muted fst-italic mb-2"><i class="bi bi-chat-quote me-1"></i>"${pref.reason}"</div>` : ''}
                            <div class="small text-muted">
                                <i class="bi bi-building me-1"></i>${pref.nom_filiere || 'N/A'} •
                                <i class="bi bi-layers me-1"></i>${pref.niveau || 'N/A'} •
                                <i class="bi bi-calendar3 me-1"></i>${pref.semestre || 'N/A'}
                            </div>
                        </div>
                        <div class="d-flex gap-1">
                            <button class="btn btn-sm btn-outline-primary edit-preference-btn" title="Modifier"
                                data-preference-id="${pref.id}"
                                data-ue-id="${pref.id_ue}"
                                data-module-id="${pref.module_id}"
                                data-module-name="${pref.module_name}"
                                data-ue-type="${pref.ue_type}"
                                data-ue-volume="${pref.volume_horaire}"
                                data-reason="${pref.reason || ''}">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-preference-btn" title="Supprimer" data-preference-id="${pref.id}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                `;
            });

            preferencesContainer.innerHTML = html;

            // Add event listeners to the edit and delete buttons
            document.querySelectorAll('.edit-preference-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const preferenceId = e.currentTarget.dataset.preferenceId;
                    const ueId = e.currentTarget.dataset.ueId;
                    const moduleId = e.currentTarget.dataset.moduleId;
                    const moduleName = e.currentTarget.dataset.moduleName;
                    const ueType = e.currentTarget.dataset.ueType;
                    const ueVolume = e.currentTarget.dataset.ueVolume;
                    const reason = e.currentTarget.dataset.reason;

                    // Fermer le modal des préférences
                    preferencesListModal.hide();

                    // Ouvrir le modal d'édition
                    setTimeout(() => {
                        openPreferenceModal(ueId, moduleId, moduleName, ueType, ueVolume, reason);
                    }, 500);
                });
            });

            document.querySelectorAll('.delete-preference-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const preferenceId = e.currentTarget.dataset.preferenceId;
                    deletePreference(preferenceId);
                });
            });
        }

        // Render preferences (alias pour la compatibilité)
        function renderPreferences() {
            renderPreferencesInModal();
        }

        // Update UE cards to show selected preferences
        function updateUECards() {
            // Reset all UE cards and checkboxes
            document.querySelectorAll('.ue-card').forEach(card => {
                card.classList.remove('selected');
            });

            document.querySelectorAll('.ue-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Clear the selected UEs set
            selectedUEs.clear();

            // Mark selected UEs
            preferences.forEach(pref => {
                const ueCard = document.querySelector(`.ue-item[data-ue-id="${pref.id_ue}"]`);
                if (ueCard) {
                    ueCard.classList.add('selected');

                    // Check the checkbox
                    const checkbox = document.querySelector(`#ue-${pref.id_ue}`);
                    if (checkbox) {
                        checkbox.checked = true;
                        selectedUEs.add(parseInt(pref.id_ue));
                    }
                }
            });

            // Update the selected count, progress bar, and select-all buttons
            updateSelectedCount();
            updateWorkloadProgress();
            updateSelectAllButtons();
        }

        // Filter modules based on search input
        function filterModules() {
            const searchTerm = moduleSearch.value.toLowerCase();

            document.querySelectorAll('.module-item').forEach(item => {
                const moduleName = item.dataset.moduleName.toLowerCase();

                if (moduleName.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Open the preference modal
        function openPreferenceModal(ueId, moduleId, moduleName, ueType, ueVolume, reason = '') {
            ueIdInput.value = ueId;
            moduleNameInput.value = moduleName;
            ueTypeInput.value = ueType;

            moduleNameDisplay.textContent = moduleName;
            ueTypeDisplay.textContent = ueType;
            ueVolumeDisplay.textContent = ueVolume;

            preferenceReasonInput.value = reason || '';

            document.getElementById('preferenceModalLabel').textContent = `Préférence pour ${ueType} de ${moduleName}`;

            preferenceModal.show();
        }

        // Save a preference
        async function savePreference() {
            const ueId = ueIdInput.value;
            const reason = preferenceReasonInput.value;

            if (!ueId) {
                showAlert('UE ID is required', 'danger');
                return;
            }

            try {
                const url = '../../route/uePreferencesRoute.php';
                const data = {
                    id_enseignant: teacherId,
                    id_ue: ueId,
                    reason: reason
                };

                console.log('Saving preference to:', url, 'Data:', data);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    return;
                }

                showAlert('Préférence enregistrée avec succès', 'success');
                preferenceModal.hide();

                // Reload preferences
                loadPreferences();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();
            } catch (error) {
                console.error('Error saving preference:', error);
                showAlert('Erreur lors de l\'enregistrement de la préférence. Veuillez réessayer.', 'danger');
            }
        }

        // Submit all preferences at once
        async function submitAllPreferences() {
            if (selectedUEs.size === 0) {
                showAlert('Veuillez sélectionner au moins une unité d\'enseignement', 'warning');
                return;
            }

            // Check workload requirement if minimum is configured
            if (minimumWorkloadHours > 0 && selectedWorkloadHours < minimumWorkloadHours) {
                const remaining = minimumWorkloadHours - selectedWorkloadHours;
                showAlert(`Charge de travail insuffisante. Vous devez sélectionner au moins ${minimumWorkloadHours} heures. Il vous reste ${remaining} heures à sélectionner.`, 'danger');
                return;
            }

            // Confirm submission
            const workloadText = minimumWorkloadHours > 0 ? ` (${selectedWorkloadHours}h/${minimumWorkloadHours}h)` : '';
            if (!confirm(`Êtes-vous sûr de vouloir soumettre ${selectedUEs.size} préférence(s)${workloadText} ? Cela remplacera toutes vos préférences existantes.`)) {
                return;
            }

            try {
                const url = '../../route/uePreferencesRoute.php?action=saveMultipleUePreferences';
                const data = {
                    id_enseignant: teacherId,
                    ue_ids: Array.from(selectedUEs)
                };

                console.log('Submitting preferences to:', url, 'Data:', data);

                // Show loading state
                submitPreferencesBtn.disabled = true;
                submitPreferencesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Enregistrement...';

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                showAlert('Préférences enregistrées avec succès', 'success');

                // Reload preferences
                loadPreferences();

                // Reset button state
                submitPreferencesBtn.disabled = false;
                updateSelectedCount();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();

            } catch (error) {
                console.error('Error submitting preferences:', error);
                showAlert('Erreur lors de l\'enregistrement des préférences. Veuillez réessayer.', 'danger');
                submitPreferencesBtn.disabled = false;
                updateSelectedCount();
            }
        }

        // Delete a preference
        async function deletePreference(preferenceId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette préférence ?')) {
                return;
            }

            try {
                const url = `../../route/uePreferencesRoute.php?preference_id=${preferenceId}`;
                console.log('Deleting preference:', url);

                const response = await fetch(url, {
                    method: 'DELETE'
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    return;
                }

                showAlert('Préférence supprimée avec succès', 'success');

                // Reload preferences
                loadPreferences();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();
            } catch (error) {
                console.error('Error deleting preference:', error);
                showAlert('Erreur lors de la suppression de la préférence. Veuillez réessayer.', 'danger');
            }
        }

        // Show an alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');

            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            alertContainer.appendChild(alert);

            // Remove the alert after 5 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alertContainer.removeChild(alert);
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>