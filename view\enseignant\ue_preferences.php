<?php
// Vérifier l'authentification
require_once '../includes/auth_check_enseignant.php';
require_once '../../model/uePreferencesModel.php';

// Get the current academic year
$academicYear = getCurrentAcademicYear();

// Get the teacher's ID from the session
$teacherId = $_SESSION['user']['teacher_id'];
// Get teacher's name from session
$prenom = $_SESSION['user']['prenom'] ?? '';
$nom = $_SESSION['user']['nom'] ?? '';
$teacherName = trim($prenom . ' ' . $nom);
if (empty($teacherName)) {
    $teacherName = $_SESSION['user']['username'] ?? 'Enseignant';
}
$specialtyName = $_SESSION['user']['specialty_name'];

// Page title
$pageTitle = "Préférences d'Unités d'Enseignement";
$currentPage = "ue_preferences.php";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        :root {
            --primary-color: #4e73df;
            --primary-light: rgba(78, 115, 223, 0.1);
            --secondary-color: #1cc88a;
            --secondary-light: rgba(28, 200, 138, 0.1);
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --dark-color: #5a5c69;
            --light-color: #f8f9fc;
            --card-border-radius: 0.5rem;
            --transition-speed: 0.3s;
        }

        /* Page Layout */
        .main-content {
            background-color: #f8f9fc;
        }

        .page-title {
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.2rem;
        }

        /* Card Styles */
        .card {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.1);
            margin-bottom: 1.5rem;
            transition: transform var(--transition-speed), box-shadow var(--transition-speed);
        }

        .card:hover {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .card-header {
            padding: 1rem 1.25rem;
            border-bottom: 1px solid #e3e6f0;
            background-color: white;
            border-top-left-radius: var(--card-border-radius) !important;
            border-top-right-radius: var(--card-border-radius) !important;
        }

        /* Module Cards */
        .module-card {
            transition: all var(--transition-speed) ease;
            border-left: 4px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .module-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }

        .module-card.selected {
            border-left: 4px solid var(--primary-color);
            background-color: var(--primary-light);
        }

        .module-card .card-body {
            padding: 1rem;
        }

        .module-card .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        /* UE Cards */
        .ue-card {
            transition: all var(--transition-speed) ease;
            border-left: 4px solid transparent;
            margin-bottom: 0.5rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.05);
            padding: 0.75rem !important;
        }

        .ue-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
        }

        .ue-card.selected {
            border-left: 4px solid var(--primary-color);
            background-color: var(--primary-light);
        }

        .ue-type {
            font-weight: 600;
            display: inline-block;
            padding: 0.15rem 0.4rem;
            border-radius: 0.25rem;
            margin-right: 0.4rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .ue-type.cours {
            background-color: rgba(78, 115, 223, 0.1);
            color: var(--primary-color);
        }

        .ue-type.td {
            background-color: rgba(28, 200, 138, 0.1);
            color: var(--secondary-color);
        }

        .ue-type.tp {
            background-color: rgba(246, 194, 62, 0.1);
            color: var(--warning-color);
        }

        .preference-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            font-size: 0.8rem;
            border-radius: 50px;
            padding: 0.25rem 0.75rem;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(78, 115, 223, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(78, 115, 223, 0);
            }
        }

        .module-info {
            font-size: 0.85rem;
            color: #6c757d;
            margin-bottom: 1rem;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }

        .module-info-item {
            display: flex;
            align-items: center;
            margin-right: 1rem;
            margin-bottom: 0.5rem;
        }

        .module-info-item:not(:last-child)::after {
            content: "•";
            margin-left: 1rem;
            color: #d1d1d1;
        }

        .module-info i {
            width: 16px;
            margin-right: 0.25rem;
            color: var(--primary-color);
        }

        /* Preference List */
        .preference-list {
            max-height: 500px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .preference-list::-webkit-scrollbar {
            width: 6px;
        }

        .preference-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .preference-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }

        .preference-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .preference-item {
            padding: 1rem;
            border-left: 4px solid var(--primary-color);
            margin-bottom: 0.75rem;
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.05);
            transition: all var(--transition-speed) ease;
        }

        .preference-item:hover {
            transform: translateX(5px);
            box-shadow: 0 0.15rem 0.5rem 0 rgba(58, 59, 69, 0.15);
        }

        .preference-item h6 {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--dark-color);
        }

        .preference-level {
            font-weight: bold;
            color: var(--primary-color);
            display: inline-block;
            padding: 0.2rem 0.5rem;
            background-color: var(--primary-light);
            border-radius: 0.25rem;
            margin-right: 0.5rem;
        }

        /* Alert Styles */
        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            max-width: 350px;
        }

        .alert {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: none;
            border-left: 4px solid;
            border-radius: 0.25rem;
            padding: 1rem;
            margin-bottom: 1rem;
            animation: slideIn 0.3s ease-out forwards;
        }

        @keyframes slideIn {
            0% {
                transform: translateX(100%);
                opacity: 0;
            }
            100% {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .alert.fade {
            animation: slideOut 0.3s ease-in forwards;
        }

        @keyframes slideOut {
            0% {
                transform: translateX(0);
                opacity: 1;
            }
            100% {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* Search Bar */
        .search-container {
            position: relative;
            max-width: 300px;
        }

        .search-container .form-control {
            padding-left: 2.5rem;
            border-radius: 50px;
            background-color: #f8f9fc;
            border: 1px solid #e3e6f0;
        }

        .search-container .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
            border-color: #bac8f3;
        }

        .search-container .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #b7b9cc;
        }

        /* Modal Styles */
        .modal-content {
            border: none;
            border-radius: var(--card-border-radius);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .modal-header {
            background-color: var(--primary-color);
            color: white;
            border-top-left-radius: var(--card-border-radius);
            border-top-right-radius: var(--card-border-radius);
        }

        .modal-header .btn-close {
            color: white;
            filter: brightness(0) invert(1);
        }

        /* Preference Floating Button */
        .preference-float-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            z-index: 1030;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Animation plus fluide */
        }

        .preference-float-btn:hover {
            transform: translateY(-5px) scale(1.05);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
            background-color: #3a5fc8; /* Slightly darker shade of primary color */
        }

        .preference-float-btn:active {
            transform: translateY(-2px) scale(0.95);
            transition: all 0.1s ease;
        }

        .preference-float-btn i {
            font-size: 1.8rem;
        }

        .preference-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #e74a3b; /* Danger/alert color */
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }

        @keyframes badgePulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        .badge-pulse {
            animation: badgePulse 0.5s ease-in-out;
        }

        /* Preferences Modal */
        .preferences-modal .modal-dialog {
            max-width: 700px;
        }

        .preferences-modal .modal-body {
            max-height: 70vh;
            overflow-y: auto;
        }

        .preferences-modal .preference-item {
            border-left: 4px solid var(--primary-color);
            background-color: white;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
            transition: all 0.3s ease;
        }

        .preferences-modal .preference-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .preferences-modal .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .preferences-modal .empty-state i {
            font-size: 3rem;
            color: #d1d1d1;
            margin-bottom: 1rem;
        }

        /* Workload Progress Bar Styles */
        .workload-progress-card {
            border-left: 4px solid var(--primary-color);
            background: linear-gradient(135deg, rgba(78, 115, 223, 0.05), rgba(78, 115, 223, 0.02));
        }

        .progress {
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
            background-size: 300% 100%;
            transition: all 0.3s ease;
            border-radius: 10px;
            font-weight: 600;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .progress-bar.insufficient {
            background: linear-gradient(90deg, #dc3545, #e74c3c);
            animation: pulse-red 2s infinite;
        }

        .progress-bar.approaching {
            background: linear-gradient(90deg, #ffc107, #f39c12);
            animation: pulse-yellow 2s infinite;
        }

        .progress-bar.sufficient {
            background: linear-gradient(90deg, #28a745, #20c997);
            animation: pulse-green 2s infinite;
        }

        @keyframes pulse-red {
            0%, 100% { box-shadow: 0 0 5px rgba(220, 53, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(220, 53, 69, 0.8); }
        }

        @keyframes pulse-yellow {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
            50% { box-shadow: 0 0 20px rgba(255, 193, 7, 0.8); }
        }

        @keyframes pulse-green {
            0%, 100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8); }
        }

        #workloadStatus.insufficient {
            background-color: var(--danger-color) !important;
        }

        #workloadStatus.approaching {
            background-color: var(--warning-color) !important;
        }

        #workloadStatus.sufficient {
            background-color: var(--secondary-color) !important;
        }

        #workloadMessage.alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #721c24;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        #workloadMessage.alert-warning {
            background-color: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.2);
            color: #856404;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        #workloadMessage.alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            color: #155724;
            border-radius: 0.375rem;
            padding: 0.5rem;
        }

        /* Submit button states */
        #submitPreferencesBtn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        #submitPreferencesBtn.insufficient {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        #submitPreferencesBtn.sufficient {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .card-header {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .search-container {
                max-width: 100%;
                width: 100%;
                margin-top: 1rem;
            }

            .workload-progress-card .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
            }

            .workload-progress-card .d-flex > * {
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Header standard -->
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="page-title"><?php echo $pageTitle; ?></h1>
                        <p class="text-muted">Sélectionnez les unités d'enseignement que vous préférez enseigner pour cette année académique</p>
                    </div>
                    <div>
                        <span class="badge bg-primary rounded-pill py-2 px-3">
                            <i class="bi bi-mortarboard-fill me-1"></i> Spécialité: <?php echo htmlspecialchars($specialtyName); ?>
                        </span>
                    </div>
                </div>

                <!-- Alert Container -->
                <div class="alert-container" id="alertContainer"></div>

                <!-- Workload Progress Bar -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card workload-progress-card">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0 fw-bold">
                                        <i class="bi bi-speedometer2 me-2"></i>Progression de la Charge de Travail
                                    </h6>
                                    <span class="badge bg-secondary" id="workloadStatus">En attente...</span>
                                </div>

                                <div class="progress mb-2" style="height: 20px;">
                                    <div class="progress-bar" id="workloadProgressBar" role="progressbar"
                                         style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                        <span id="progressPercentage">0%</span>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <span id="selectedHours">0</span> heures sélectionnées /
                                        <span id="minimumHours">--</span> heures minimum requises
                                    </small>
                                    <small class="text-muted" id="remainingHours">
                                        -- heures restantes
                                    </small>
                                </div>

                                <div id="workloadMessage" class="mt-2 small" style="display: none;">
                                    <!-- Dynamic message will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Available Modules Column (Full Width) -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center py-2">
                                <h5 class="mb-0"><i class="bi bi-grid-3x3-gap-fill me-2"></i>Modules et Unités d'Enseignement</h5>
                                <div class="d-flex align-items-center">
                                    <div class="search-container me-3">
                                        <i class="bi bi-search search-icon"></i>
                                        <input type="text" class="form-control" id="moduleSearch" placeholder="Rechercher un module...">
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-3">
                                <div class="row g-2" id="modulesContainer">
                                    <!-- Modules will be loaded here dynamically -->
                                    <div class="col-12 text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                        <p class="mt-2">Chargement des modules...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bouton de soumission global -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button id="submitPreferencesBtn" class="btn btn-primary btn-lg">
                            <i class="bi bi-save me-2"></i> Soumettre mes préférences
                        </button>
                    </div>
                </div>
            </div>

            <!-- Bouton flottant pour accéder aux préférences -->
            <div class="preference-float-btn" id="preferenceFloatBtn" data-bs-toggle="modal" data-bs-target="#preferencesListModal"
                 data-bs-title="Voir mes préférences d'UE" data-bs-placement="left">
                <i class="bi bi-bookmark-check"></i>
                <span class="preference-badge" id="preferenceCartBadge">0</span>
            </div>
        </div>
    </div>

    <!-- UE Preference Modal -->
    <div class="modal fade" id="preferenceModal" tabindex="-1" aria-labelledby="preferenceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="preferenceModalLabel">Ajouter une préférence</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="preferenceForm">
                        <input type="hidden" id="ueId" name="ueId">
                        <input type="hidden" id="moduleName" name="moduleName">
                        <input type="hidden" id="ueType" name="ueType">

                        <div class="mb-3">
                            <h6 class="fw-bold">Module: <span id="moduleNameDisplay"></span></h6>
                            <p class="mb-1">Type d'UE: <span id="ueTypeDisplay" class="badge bg-primary"></span></p>
                            <p class="mb-1">Volume horaire: <span id="ueVolumeDisplay"></span> heures</p>
                        </div>



                        <div class="mb-3">
                            <label for="preferenceReason" class="form-label fw-bold">Raison (optionnel)</label>
                            <textarea class="form-control" id="preferenceReason" name="preferenceReason" rows="3" placeholder="Expliquez pourquoi vous préférez enseigner cette unité d'enseignement..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="savePreferenceBtn">
                        <i class="bi bi-check-circle me-1"></i>Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Preferences List Modal -->
    <div class="modal fade preferences-modal" id="preferencesListModal" tabindex="-1" aria-labelledby="preferencesListModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="preferencesListModalLabel">
                        <i class="bi bi-basket me-2"></i>Mes Préférences d'Unités d'Enseignement
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="preferencesModalContainer">
                        <!-- Preferences will be loaded here dynamically -->
                        <div class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Chargement...</span>
                            </div>
                            <p class="mt-2">Chargement des préférences...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Fermer
                    </button>
                    <button type="button" class="btn btn-primary" id="submitModalPreferencesBtn" data-bs-dismiss="modal">
                        <i class="bi bi-save me-1"></i>Soumettre mes préférences
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Global variables
        const teacherId = <?php echo $teacherId; ?>;
        const academicYear = '<?php echo $academicYear; ?>';
        const teacherRole = '<?php echo $_SESSION['user']['role'] ?? 'enseignant'; ?>'; // Get teacher role from session
        let modules = [];
        let preferences = [];
        let selectedUEs = new Set(); // Pour stocker les UEs sélectionnées
        let minimumWorkloadHours = 0; // Minimum required hours
        let selectedWorkloadHours = 0; // Currently selected hours

        // DOM elements
        const modulesContainer = document.getElementById('modulesContainer');
        const preferencesContainer = document.getElementById('preferencesModalContainer');
        const moduleSearch = document.getElementById('moduleSearch');
        const preferenceModal = new bootstrap.Modal(document.getElementById('preferenceModal'));
        const preferencesListModal = new bootstrap.Modal(document.getElementById('preferencesListModal'));
        const preferenceForm = document.getElementById('preferenceForm');
        const ueIdInput = document.getElementById('ueId');
        const moduleNameInput = document.getElementById('moduleName');
        const ueTypeInput = document.getElementById('ueType');
        const moduleNameDisplay = document.getElementById('moduleNameDisplay');
        const ueTypeDisplay = document.getElementById('ueTypeDisplay');
        const ueVolumeDisplay = document.getElementById('ueVolumeDisplay');
        const preferenceReasonInput = document.getElementById('preferenceReason');
        const savePreferenceBtn = document.getElementById('savePreferenceBtn');
        const submitPreferencesBtn = document.getElementById('submitPreferencesBtn');
        const submitModalPreferencesBtn = document.getElementById('submitModalPreferencesBtn');
        const preferenceCartBadge = document.getElementById('preferenceCartBadge');

        // Progress bar elements
        const workloadProgressBar = document.getElementById('workloadProgressBar');
        const progressPercentage = document.getElementById('progressPercentage');
        const workloadStatus = document.getElementById('workloadStatus');
        const selectedHours = document.getElementById('selectedHours');
        const minimumHours = document.getElementById('minimumHours');
        const remainingHours = document.getElementById('remainingHours');
        const workloadMessage = document.getElementById('workloadMessage');

        // Load modules and preferences when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            loadMinimumWorkload();
            loadModulesAndUEs();
            loadPreferences();

            // Add event listeners
            moduleSearch.addEventListener('input', filterModules);
            savePreferenceBtn.addEventListener('click', savePreference);
            submitPreferencesBtn.addEventListener('click', submitAllPreferences);
            submitModalPreferencesBtn.addEventListener('click', submitAllPreferences);

            // Délégation d'événements pour les cases à cocher
            document.addEventListener('change', function(e) {
                if (e.target && e.target.classList.contains('ue-checkbox')) {
                    handleCheckboxChange(e.target);
                }
            });

            // Initialiser le badge du panier
            updatePreferenceCartBadge();

            // Initialiser les tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Ajouter un événement pour ouvrir le modal des préférences
            document.getElementById('preferenceFloatBtn').addEventListener('click', function() {
                renderPreferencesInModal();
            });
        });

        // Load minimum workload configuration for the teacher
        async function loadMinimumWorkload() {
            try {
                const url = `../../controller/configurationChargeController.php?action=getMinimumWorkloadForTeacher&role=${teacherRole}&academic_year=${academicYear}`;
                console.log('Fetching minimum workload from:', url);

                const response = await fetch(url);

                if (!response.ok) {
                    console.error('Server response not OK:', response.status);
                    // Set default minimum if no configuration found
                    minimumWorkloadHours = 0;
                    updateWorkloadProgress();
                    return;
                }

                const result = await response.json();

                if (result.error) {
                    console.warn('No minimum workload configuration found:', result.error);
                    // Set default minimum if no configuration found
                    minimumWorkloadHours = 0;
                } else {
                    minimumWorkloadHours = parseInt(result.data.charge_minimale) || 0;
                }

                updateWorkloadProgress();
            } catch (error) {
                console.error('Error loading minimum workload:', error);
                minimumWorkloadHours = 0;
                updateWorkloadProgress();
            }
        }

        // Update workload progress bar
        function updateWorkloadProgress() {
            // Calculate selected hours from checked UEs
            selectedWorkloadHours = 0;
            document.querySelectorAll('.ue-checkbox:checked').forEach(checkbox => {
                const ueVolume = parseInt(checkbox.dataset.ueVolume) || 0;
                selectedWorkloadHours += ueVolume;
            });

            // Update display elements
            selectedHours.textContent = selectedWorkloadHours;
            minimumHours.textContent = minimumWorkloadHours || '--';

            // Calculate progress percentage
            let progressPercent = 0;
            if (minimumWorkloadHours > 0) {
                progressPercent = Math.min((selectedWorkloadHours / minimumWorkloadHours) * 100, 100);
            }

            // Update progress bar
            workloadProgressBar.style.width = progressPercent + '%';
            workloadProgressBar.setAttribute('aria-valuenow', progressPercent);
            progressPercentage.textContent = Math.round(progressPercent) + '%';

            // Calculate remaining hours
            const remaining = Math.max(minimumWorkloadHours - selectedWorkloadHours, 0);
            remainingHours.textContent = remaining > 0 ? `${remaining} heures restantes` : 'Objectif atteint';

            // Update status and styling based on progress
            updateWorkloadStatus(progressPercent, remaining);
        }

        // Update workload status and styling
        function updateWorkloadStatus(progressPercent, remaining) {
            // Remove existing classes
            workloadProgressBar.classList.remove('insufficient', 'approaching', 'sufficient');
            workloadStatus.classList.remove('insufficient', 'approaching', 'sufficient');
            submitPreferencesBtn.classList.remove('insufficient', 'sufficient');
            workloadMessage.classList.remove('alert-danger', 'alert-warning', 'alert-success');

            let statusText = '';
            let messageText = '';
            let messageClass = '';

            if (minimumWorkloadHours === 0) {
                // No minimum configuration found
                statusText = 'Non configuré';
                messageText = 'Aucune configuration de charge minimale trouvée pour votre rôle.';
                messageClass = 'alert-warning';
                workloadStatus.className = 'badge bg-secondary';
                submitPreferencesBtn.disabled = selectedUEs.size === 0;
            } else if (progressPercent < 80) {
                // Insufficient (less than 80% of minimum)
                statusText = 'Insuffisant';
                messageText = `Vous devez sélectionner au moins ${minimumWorkloadHours} heures. Il vous reste ${remaining} heures à sélectionner.`;
                messageClass = 'alert-danger';
                workloadProgressBar.classList.add('insufficient');
                workloadStatus.classList.add('insufficient');
                submitPreferencesBtn.classList.add('insufficient');
                submitPreferencesBtn.disabled = true;
            } else if (progressPercent < 100) {
                // Approaching (80-99% of minimum)
                statusText = 'Proche';
                messageText = `Vous êtes proche de l'objectif. Il vous reste ${remaining} heures à sélectionner.`;
                messageClass = 'alert-warning';
                workloadProgressBar.classList.add('approaching');
                workloadStatus.classList.add('approaching');
                submitPreferencesBtn.disabled = true;
            } else {
                // Sufficient (100% or more of minimum)
                statusText = 'Suffisant';
                messageText = 'Félicitations ! Vous avez atteint la charge minimale requise.';
                messageClass = 'alert-success';
                workloadProgressBar.classList.add('sufficient');
                workloadStatus.classList.add('sufficient');
                submitPreferencesBtn.classList.add('sufficient');
                submitPreferencesBtn.disabled = false;
            }

            // Update status badge
            workloadStatus.textContent = statusText;

            // Update message
            if (messageText) {
                workloadMessage.textContent = messageText;
                workloadMessage.className = `mt-2 small ${messageClass}`;
                workloadMessage.style.display = 'block';
            } else {
                workloadMessage.style.display = 'none';
            }
        }

        // Gérer le changement d'état des cases à cocher
        function handleCheckboxChange(checkbox) {
            const ueId = parseInt(checkbox.value);

            if (checkbox.checked) {
                selectedUEs.add(ueId);
            } else {
                selectedUEs.delete(ueId);
            }

            // Mettre à jour l'interface
            updateSelectedCount();
            updateWorkloadProgress(); // Update progress bar
        }

        // Mettre à jour le compteur de sélection
        function updateSelectedCount() {
            const count = selectedUEs.size;
            submitPreferencesBtn.textContent = `Soumettre mes préférences (${count} sélectionnée${count !== 1 ? 's' : ''})`;
            submitPreferencesBtn.disabled = count === 0;
            submitModalPreferencesBtn.textContent = `Soumettre mes préférences (${count} sélectionnée${count !== 1 ? 's' : ''})`;
            submitModalPreferencesBtn.disabled = count === 0;

            // Mettre à jour le badge du panier
            updatePreferenceCartBadge();
        }

        // Mettre à jour le badge du bouton flottant de préférences
        function updatePreferenceCartBadge() {
            const count = selectedUEs.size;
            preferenceCartBadge.textContent = count;

            if (count > 0) {
                preferenceCartBadge.style.display = 'flex';
                // Ajouter une animation subtile pour attirer l'attention
                preferenceCartBadge.classList.add('badge-pulse');
                setTimeout(() => {
                    preferenceCartBadge.classList.remove('badge-pulse');
                }, 1000);
            } else {
                preferenceCartBadge.style.display = 'none';
            }
        }

        // Load modules and their teaching units matching the teacher's specialty
        async function loadModulesAndUEs() {
            try {
                const url = `../../route/uePreferencesRoute.php?action=getModulesAndUEsByTeacherSpecialty&teacher_id=${teacherId}`;
                console.log('Fetching modules and UEs from:', url);

                const response = await fetch(url);

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Server error: ${response.status}</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Error parsing server response</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">${result.error}</p></div>`;
                    return;
                }

                modules = result.data;
                renderModules();
            } catch (error) {
                console.error('Error loading modules and UEs:', error);
                showAlert('Erreur lors du chargement des modules et UEs. Veuillez réessayer.', 'danger');
                modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p class="text-danger">Erreur lors du chargement des modules et UEs. Veuillez réessayer.</p></div>`;
            }
        }

        // Load the teacher's UE preferences
        async function loadPreferences() {
            try {
                const url = `../../route/uePreferencesRoute.php?action=getTeacherUePreferences&teacher_id=${teacherId}`;
                console.log('Fetching preferences from:', url);

                const response = await fetch(url);

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Server error: ${response.status}</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Error parsing server response</p><pre class="small text-start">${text}</pre></div>`;
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">${result.error}</p></div>`;
                    return;
                }

                preferences = result.data;
                renderPreferences();
                updateUECards();
            } catch (error) {
                console.error('Error loading preferences:', error);
                showAlert('Erreur lors du chargement des préférences. Veuillez réessayer.', 'danger');
                preferencesContainer.innerHTML = `<div class="text-center py-5"><p class="text-danger">Erreur lors du chargement des préférences. Veuillez réessayer.</p></div>`;
            }
        }

        // Render modules and their teaching units in the modules container
        function renderModules() {
            if (modules.length === 0) {
                modulesContainer.innerHTML = `<div class="col-12 text-center py-5"><p>Aucun module correspondant à votre spécialité n'a été trouvé.</p></div>`;
                return;
            }

            let html = '';

            modules.forEach(module => {
                // Check if the module has teaching units
                if (!module.teaching_units || module.teaching_units.length === 0) {
                    return; // Skip modules without teaching units
                }

                html += `
                <div class="col-12 module-item" data-module-id="${module.id}" data-module-name="${module.nom}">
                    <div class="card module-card mb-3">
                        <div class="card-body">
                            <h5 class="card-title">${module.nom}</h5>
                            <div class="module-info">
                                <div class="module-info-item"><i class="bi bi-building"></i> ${module.nom_filiere || 'N/A'}</div>
                                <div class="module-info-item"><i class="bi bi-layers"></i> ${module.niveau || 'N/A'}</div>
                                <div class="module-info-item"><i class="bi bi-calendar3"></i> ${module.semestre || 'N/A'}</div>
                                <div class="module-info-item"><i class="bi bi-clock"></i> ${module.volume_total || 'N/A'} heures</div>
                            </div>
                            <div class="ue-list mt-2">
                                <h6 class="mb-1 fw-bold small text-uppercase">Unités d'Enseignement:</h6>
                                <div class="row g-1">
                `;

                // Add teaching units for this module
                module.teaching_units.forEach(ue => {
                    const ueTypeClass = ue.type === 'Cours' ? 'cours' : (ue.type === 'TD' ? 'td' : 'tp');

                    html += `
                    <div class="col-12">
                        <div class="ue-card ue-item" data-ue-id="${ue.id}" data-module-id="${module.id}" data-module-name="${module.nom}" data-ue-type="${ue.type}" data-ue-volume="${ue.volume_horaire}">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <div class="form-check me-2">
                                        <input class="form-check-input ue-checkbox" type="checkbox" value="${ue.id}"
                                            id="ue-${ue.id}" data-ue-id="${ue.id}" data-module-id="${module.id}"
                                            data-module-name="${module.nom}" data-ue-type="${ue.type}" data-ue-volume="${ue.volume_horaire}">
                                    </div>
                                    <div>
                                        <span class="ue-type ${ueTypeClass}">${ue.type}</span>
                                        <span class="small text-muted">${ue.volume_horaire} h</span>
                                        ${ue.nb_groupes > 1 ? `<span class="small text-muted ms-2">(${ue.nb_groupes} groupes)</span>` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    `;
                });

                html += `
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            });

            modulesContainer.innerHTML = html;

            // Add event listeners to the add preference buttons
            document.querySelectorAll('.add-preference-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const ueId = e.currentTarget.dataset.ueId;
                    const moduleId = e.currentTarget.dataset.moduleId;
                    const moduleName = e.currentTarget.dataset.moduleName;
                    const ueType = e.currentTarget.dataset.ueType;
                    const ueVolume = e.currentTarget.dataset.ueVolume;
                    openPreferenceModal(ueId, moduleId, moduleName, ueType, ueVolume);
                });
            });

            // Update UE cards to show selected preferences
            updateUECards();
        }

        // Render preferences in the modal
        function renderPreferencesInModal() {
            if (preferences.length === 0) {
                preferencesContainer.innerHTML = `
                <div class="empty-state">
                    <i class="bi bi-basket"></i>
                    <h5>Votre panier est vide</h5>
                    <p class="text-muted">Vous n'avez pas encore sélectionné de préférences.</p>
                </div>`;
                return;
            }

            // Sort preferences by module name and UE type
            preferences.sort((a, b) => {
                if (a.module_name !== b.module_name) {
                    return a.module_name.localeCompare(b.module_name);
                }
                return a.ue_type.localeCompare(b.ue_type);
            });

            let html = '';

            preferences.forEach(pref => {
                const ueTypeClass = pref.ue_type === 'Cours' ? 'cours' : (pref.ue_type === 'TD' ? 'td' : 'tp');

                html += `
                <div class="preference-item p-3 mb-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${pref.module_name}</h6>
                            <p class="mb-1">
                                <span class="ue-type ${ueTypeClass}">${pref.ue_type}</span>
                                <span class="small text-muted">${pref.volume_horaire} heures</span>
                            </p>
                            ${pref.reason ? `<p class="mb-1 small text-muted fst-italic">"${pref.reason}"</p>` : ''}
                            <div class="module-info small">
                                <div class="module-info-item"><i class="bi bi-building"></i> ${pref.nom_filiere || 'N/A'}</div>
                                <div class="module-info-item"><i class="bi bi-layers"></i> ${pref.niveau || 'N/A'}</div>
                                <div class="module-info-item"><i class="bi bi-calendar3"></i> ${pref.semestre || 'N/A'}</div>
                            </div>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary edit-preference-btn"
                                data-preference-id="${pref.id}"
                                data-ue-id="${pref.id_ue}"
                                data-module-id="${pref.module_id}"
                                data-module-name="${pref.module_name}"
                                data-ue-type="${pref.ue_type}"
                                data-ue-volume="${pref.volume_horaire}"
                                data-reason="${pref.reason || ''}">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger delete-preference-btn" data-preference-id="${pref.id}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
                `;
            });

            preferencesContainer.innerHTML = html;

            // Add event listeners to the edit and delete buttons
            document.querySelectorAll('.edit-preference-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const preferenceId = e.currentTarget.dataset.preferenceId;
                    const ueId = e.currentTarget.dataset.ueId;
                    const moduleId = e.currentTarget.dataset.moduleId;
                    const moduleName = e.currentTarget.dataset.moduleName;
                    const ueType = e.currentTarget.dataset.ueType;
                    const ueVolume = e.currentTarget.dataset.ueVolume;
                    const reason = e.currentTarget.dataset.reason;

                    // Fermer le modal des préférences
                    preferencesListModal.hide();

                    // Ouvrir le modal d'édition
                    setTimeout(() => {
                        openPreferenceModal(ueId, moduleId, moduleName, ueType, ueVolume, reason);
                    }, 500);
                });
            });

            document.querySelectorAll('.delete-preference-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const preferenceId = e.currentTarget.dataset.preferenceId;
                    deletePreference(preferenceId);
                });
            });
        }

        // Render preferences (alias pour la compatibilité)
        function renderPreferences() {
            renderPreferencesInModal();
        }

        // Update UE cards to show selected preferences
        function updateUECards() {
            // Reset all UE cards and checkboxes
            document.querySelectorAll('.ue-card').forEach(card => {
                card.classList.remove('selected');
            });

            document.querySelectorAll('.ue-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Clear the selected UEs set
            selectedUEs.clear();

            // Mark selected UEs
            preferences.forEach(pref => {
                const ueCard = document.querySelector(`.ue-item[data-ue-id="${pref.id_ue}"]`);
                if (ueCard) {
                    ueCard.classList.add('selected');

                    // Check the checkbox
                    const checkbox = document.querySelector(`#ue-${pref.id_ue}`);
                    if (checkbox) {
                        checkbox.checked = true;
                        selectedUEs.add(parseInt(pref.id_ue));
                    }
                }
            });

            // Update the selected count and progress bar
            updateSelectedCount();
            updateWorkloadProgress();
        }

        // Filter modules based on search input
        function filterModules() {
            const searchTerm = moduleSearch.value.toLowerCase();

            document.querySelectorAll('.module-item').forEach(item => {
                const moduleName = item.dataset.moduleName.toLowerCase();

                if (moduleName.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        // Open the preference modal
        function openPreferenceModal(ueId, moduleId, moduleName, ueType, ueVolume, reason = '') {
            ueIdInput.value = ueId;
            moduleNameInput.value = moduleName;
            ueTypeInput.value = ueType;

            moduleNameDisplay.textContent = moduleName;
            ueTypeDisplay.textContent = ueType;
            ueVolumeDisplay.textContent = ueVolume;

            preferenceReasonInput.value = reason || '';

            document.getElementById('preferenceModalLabel').textContent = `Préférence pour ${ueType} de ${moduleName}`;

            preferenceModal.show();
        }

        // Save a preference
        async function savePreference() {
            const ueId = ueIdInput.value;
            const reason = preferenceReasonInput.value;

            if (!ueId) {
                showAlert('UE ID is required', 'danger');
                return;
            }

            try {
                const url = '../../route/uePreferencesRoute.php';
                const data = {
                    id_enseignant: teacherId,
                    id_ue: ueId,
                    reason: reason
                };

                console.log('Saving preference to:', url, 'Data:', data);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    return;
                }

                showAlert('Préférence enregistrée avec succès', 'success');
                preferenceModal.hide();

                // Reload preferences
                loadPreferences();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();
            } catch (error) {
                console.error('Error saving preference:', error);
                showAlert('Erreur lors de l\'enregistrement de la préférence. Veuillez réessayer.', 'danger');
            }
        }

        // Submit all preferences at once
        async function submitAllPreferences() {
            if (selectedUEs.size === 0) {
                showAlert('Veuillez sélectionner au moins une unité d\'enseignement', 'warning');
                return;
            }

            // Check workload requirement if minimum is configured
            if (minimumWorkloadHours > 0 && selectedWorkloadHours < minimumWorkloadHours) {
                const remaining = minimumWorkloadHours - selectedWorkloadHours;
                showAlert(`Charge de travail insuffisante. Vous devez sélectionner au moins ${minimumWorkloadHours} heures. Il vous reste ${remaining} heures à sélectionner.`, 'danger');
                return;
            }

            // Confirm submission
            const workloadText = minimumWorkloadHours > 0 ? ` (${selectedWorkloadHours}h/${minimumWorkloadHours}h)` : '';
            if (!confirm(`Êtes-vous sûr de vouloir soumettre ${selectedUEs.size} préférence(s)${workloadText} ? Cela remplacera toutes vos préférences existantes.`)) {
                return;
            }

            try {
                const url = '../../route/uePreferencesRoute.php?action=saveMultipleUePreferences';
                const data = {
                    id_enseignant: teacherId,
                    ue_ids: Array.from(selectedUEs)
                };

                console.log('Submitting preferences to:', url, 'Data:', data);

                // Show loading state
                submitPreferencesBtn.disabled = true;
                submitPreferencesBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Enregistrement...';

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    submitPreferencesBtn.disabled = false;
                    updateSelectedCount();
                    return;
                }

                showAlert('Préférences enregistrées avec succès', 'success');

                // Reload preferences
                loadPreferences();

                // Reset button state
                submitPreferencesBtn.disabled = false;
                updateSelectedCount();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();

            } catch (error) {
                console.error('Error submitting preferences:', error);
                showAlert('Erreur lors de l\'enregistrement des préférences. Veuillez réessayer.', 'danger');
                submitPreferencesBtn.disabled = false;
                updateSelectedCount();
            }
        }

        // Delete a preference
        async function deletePreference(preferenceId) {
            if (!confirm('Êtes-vous sûr de vouloir supprimer cette préférence ?')) {
                return;
            }

            try {
                const url = `../../route/uePreferencesRoute.php?preference_id=${preferenceId}`;
                console.log('Deleting preference:', url);

                const response = await fetch(url, {
                    method: 'DELETE'
                });

                // Check if response is OK
                if (!response.ok) {
                    const text = await response.text();
                    console.error('Server response not OK:', response.status, text);
                    showAlert(`Server error: ${response.status}`, 'danger');
                    return;
                }

                // Try to parse JSON
                const text = await response.text();
                let result;
                try {
                    result = JSON.parse(text);
                } catch (e) {
                    console.error('Error parsing JSON:', e, 'Response text:', text);
                    showAlert('Error parsing server response', 'danger');
                    return;
                }

                if (result.error) {
                    showAlert(result.error, 'danger');
                    return;
                }

                showAlert('Préférence supprimée avec succès', 'success');

                // Reload preferences
                loadPreferences();

                // Mettre à jour le badge du panier et la barre de progression
                updatePreferenceCartBadge();
                updateWorkloadProgress();
            } catch (error) {
                console.error('Error deleting preference:', error);
                showAlert('Erreur lors de la suppression de la préférence. Veuillez réessayer.', 'danger');
            }
        }

        // Show an alert
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');

            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            alertContainer.appendChild(alert);

            // Remove the alert after 5 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    alertContainer.removeChild(alert);
                }, 300);
            }, 5000);
        }
    </script>
</body>
</html>