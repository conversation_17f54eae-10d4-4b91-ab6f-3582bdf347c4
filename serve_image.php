<?php
// Activer l'affichage des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure l'utilitaire de chemin
require_once __DIR__ . '/utils/path_utils.php';

// Fonction pour journaliser les erreurs en mode debug uniquement
function logError($message) {
    if (isset($_GET['debug']) && $_GET['debug'] === '1') {
        echo $message;
        exit;
    }
}

// Vérifier si un fichier a été spécifié
if (!isset($_GET['file']) || empty($_GET['file'])) {
    logError("Erreur: Aucun fichier spécifié");
    header("HTTP/1.0 400 Bad Request");
    exit;
}

// Récupérer le chemin du fichier et le nettoyer pour éviter les attaques par traversée de chemin
$filePath = $_GET['file'];

// Déterminer si c'est un chemin complet ou juste un nom de fichier
if (strpos($filePath, '/') !== false) {
    // C'est un chemin relatif (ex: assets/img/profile/image.jpg)
    $relativePath = $filePath;
    $filename = basename($filePath);
} else {
    // C'est juste un nom de fichier
    $filename = $filePath;
    $relativePath = 'assets/img/profile/' . $filename;
}

// Obtenir le chemin de base du projet
$basePath = getBasePath();
$profileImgPath = 'view/assets/img/profile/';

// Définir les chemins possibles pour trouver l'image
$paths = [
    // Chemins basés sur le nom de fichier
    $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $profileImgPath . $filename,
    __DIR__ . '/' . $profileImgPath . $filename,

    // Chemins basés sur le chemin relatif
    $_SERVER['DOCUMENT_ROOT'] . $basePath . '/' . $relativePath,
    __DIR__ . '/' . $relativePath
];



// Chercher le fichier dans les différents chemins
$filePath = null;
foreach ($paths as $path) {
    if (file_exists($path)) {
        $filePath = $path;
        break;
    }
}

// Si le fichier n'a pas été trouvé, servir l'image par défaut
if ($filePath === null) {
    logError("Erreur: Fichier non trouvé. Chemins vérifiés: " . implode(", ", $paths));

    // Servir l'image par défaut
    $defaultImage = $_SERVER['DOCUMENT_ROOT'] . $basePath . '/view/assets/img/default-profile.svg';
    $altDefaultImage = __DIR__ . '/view/assets/img/default-profile.svg';
    if (file_exists($defaultImage)) {
        $filePath = $defaultImage;
    } elseif (file_exists($altDefaultImage)) {
        $filePath = $altDefaultImage;
    } else {
        // Si aucune image par défaut n'est trouvée, renvoyer une erreur 404
        header("HTTP/1.0 404 Not Found");
        exit;
    }
}

// Déterminer le type MIME
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mime = finfo_file($finfo, $filePath);
finfo_close($finfo);

// Servir l'image avec les en-têtes appropriés
header("Content-Type: $mime");
header("Content-Length: " . filesize($filePath));
header("Cache-Control: no-cache, must-revalidate");
header("Expires: 0");
header("Pragma: no-cache");

// Lire et afficher le fichier
readfile($filePath);
?>
