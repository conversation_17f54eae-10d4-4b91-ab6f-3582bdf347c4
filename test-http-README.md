# Guide d'utilisation du fichier test.http

Ce fichier contient des requêtes HTTP pour tester les API des tables `notifications` et `messages`. Il est conçu pour être utilisé avec des extensions comme "REST Client" dans VS Code ou d'autres outils similaires.

## Prérequis

1. **Extension REST Client** : Si vous utilisez VS Code, installez l'extension "REST Client" par Huachao Mao.
2. **Serveur local** : Assurez-vous que votre serveur local (XAMPP, WAMP, etc.) est en cours d'exécution.
3. **Projet configuré** : Le projet doit être correctement configuré dans votre serveur local.

## Configuration

Avant d'utiliser le fichier, vérifiez et modifiez si nécessaire la variable `@baseUrl` au début du fichier pour qu'elle corresponde à l'URL de base de votre projet.

```
@baseUrl = http://localhost/Projet-Web
```

## Utilisation

### Avec l'extension REST Client dans VS Code

1. Ouvrez le fichier `test.http` dans VS Code.
2. Cliquez sur le lien "Send Request" qui apparaît au-dessus de chaque requête.
3. Les résultats s'afficheront dans un nouvel onglet.

### Avec Postman ou d'autres outils

Vous pouvez copier les URL et les corps des requêtes dans Postman ou d'autres outils similaires.

## Structure des requêtes

### Notifications

1. **Récupérer toutes les notifications** : `GET {{notificationsRoute}}?action=getAll`
2. **Créer une nouvelle notification** : `POST {{notificationsRoute}}?action=createNotification`
3. **Marquer une notification comme lue** : `POST {{notificationsRoute}}?action=markAsRead`
4. **Marquer toutes les notifications comme lues** : `POST {{notificationsRoute}}?action=markAllAsRead`
5. **Supprimer une notification** : `POST {{notificationsRoute}}?action=deleteNotification`
6. **Récupérer le nombre de notifications non lues** : `GET {{notificationsRoute}}?action=getUnreadCount`

### Messages

1. **Récupérer tous les messages** : `GET {{messagesRoute}}?action=getAll&page=1&perPage=10`
2. **Créer un nouveau message** : `POST {{messagesRoute}}?action=createMessage`
3. **Créer un message administratif** : `POST {{messagesRoute}}?action=createAdminMessage`
4. **Marquer un message comme lu** : `POST {{messagesRoute}}?action=markAsRead`
5. **Marquer tous les messages comme lus** : `POST {{messagesRoute}}?action=markAllAsRead`
6. **Supprimer un message** : `POST {{messagesRoute}}?action=deleteMessage`
7. **Récupérer le nombre de messages non lus** : `GET {{messagesRoute}}?action=getUnreadCount`
8. **Vérifier si la base de données a besoin d'être mise à jour** : `GET {{messagesRoute}}?action=checkIfDatabaseNeedsUpdate`

## Adaptation des requêtes

N'hésitez pas à modifier les corps des requêtes pour tester différents scénarios. Par exemple, vous pouvez changer les valeurs des champs `id`, `title`, `message`, etc.

## Dépannage

Si vous rencontrez des erreurs :

1. Vérifiez que votre serveur local est en cours d'exécution.
2. Vérifiez que l'URL de base est correcte.
3. Vérifiez que les routes et les actions existent dans votre projet.
4. Consultez les logs du serveur pour plus d'informations sur les erreurs.
