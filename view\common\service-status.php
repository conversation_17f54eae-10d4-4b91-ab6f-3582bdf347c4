<?php
/**
 * Page d'état des services - Interface cohérente pour les services désactivés
 */

// Vérifier l'authentification
if (!isset($_SESSION['user_id'])) {
    header("Location: " . BASE_URL . "/view/auth/login.php");
    exit();
}

// Récupérer les informations du service depuis les paramètres
$serviceKey = $_GET['service'] ?? 'unknown';
$serviceName = $_GET['name'] ?? 'Service';
$reason = $_GET['reason'] ?? 'temporarily_unavailable';
$userRole = $_SESSION['user_role'] ?? 'user';

// Configuration des messages selon la raison
$statusConfig = [
    'temporarily_unavailable' => [
        'icon' => 'bi-clock-history',
        'color' => 'warning',
        'title' => 'Service Temporairement Indisponible',
        'description' => 'Ce service n\'est pas disponible pour le moment.'
    ],
    'maintenance' => [
        'icon' => 'bi-tools',
        'color' => 'info',
        'title' => 'Maintenance en Cours',
        'description' => 'Ce service est actuellement en maintenance.'
    ],
    'period_closed' => [
        'icon' => 'bi-calendar-x',
        'color' => 'warning',
        'title' => 'Période Fermée',
        'description' => 'La période d\'accès à ce service est actuellement fermée.'
    ],
    'not_activated' => [
        'icon' => 'bi-pause-circle',
        'color' => 'secondary',
        'title' => 'Service Non Activé',
        'description' => 'Ce service n\'a pas encore été activé par l\'administration.'
    ]
];

$config = $statusConfig[$reason] ?? $statusConfig['temporarily_unavailable'];

// Définir le titre de la page
$pageTitle = $serviceName . " - " . $config['title'];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - UniAdmin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .service-status-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .status-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .status-header {
            padding: 2rem;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .status-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .status-body {
            padding: 2rem;
        }
        
        .info-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .contact-info {
            background-color: #e7f3ff;
            border-left: 4px solid #0066cc;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 2rem;
        }
        
        .refresh-button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .refresh-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,123,255,0.3);
            color: white;
        }
        
        .status-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }
        
        .detail-item {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        
        .detail-icon {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>
        
        <div class="main-content">
            <?php include '../includes/header.php'; ?>
            
            <div class="container-fluid mt-4">
                <div class="service-status-container">
                    <div class="card status-card">
                        <!-- En-tête du statut -->
                        <div class="status-header">
                            <div class="status-icon text-<?php echo $config['color']; ?>">
                                <i class="<?php echo $config['icon']; ?>"></i>
                            </div>
                            <h2 class="mb-2"><?php echo $config['title']; ?></h2>
                            <h4 class="text-muted mb-0"><?php echo htmlspecialchars($serviceName); ?></h4>
                        </div>
                        
                        <!-- Corps du message -->
                        <div class="status-body">
                            <div class="text-center mb-4">
                                <p class="lead"><?php echo $config['description']; ?></p>
                            </div>
                            
                            <!-- Détails du statut -->
                            <div class="status-details">
                                <div class="detail-item">
                                    <div class="detail-icon text-primary">
                                        <i class="bi bi-info-circle"></i>
                                    </div>
                                    <h6>Statut Actuel</h6>
                                    <span class="badge bg-<?php echo $config['color']; ?>">
                                        <?php echo $config['title']; ?>
                                    </span>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-icon text-info">
                                        <i class="bi bi-person-badge"></i>
                                    </div>
                                    <h6>Votre Rôle</h6>
                                    <span class="text-capitalize">
                                        <?php echo htmlspecialchars($userRole); ?>
                                    </span>
                                </div>
                                
                                <div class="detail-item">
                                    <div class="detail-icon text-success">
                                        <i class="bi bi-clock"></i>
                                    </div>
                                    <h6>Dernière Vérification</h6>
                                    <span><?php echo date('H:i:s'); ?></span>
                                </div>
                            </div>
                            
                            <!-- Informations contextuelles -->
                            <div class="info-section">
                                <h5><i class="bi bi-lightbulb text-warning"></i> Que faire maintenant ?</h5>
                                
                                <?php if ($reason === 'period_closed'): ?>
                                    <ul class="mb-0">
                                        <li>La période de soumission est actuellement fermée</li>
                                        <li>Contactez votre chef de département pour plus d'informations</li>
                                        <li>Vérifiez régulièrement ou attendez l'annonce de réouverture</li>
                                        <li>Préparez vos données en attendant la prochaine période</li>
                                    </ul>
                                <?php elseif ($reason === 'maintenance'): ?>
                                    <ul class="mb-0">
                                        <li>Le service est temporairement en maintenance</li>
                                        <li>Aucune action n'est requise de votre part</li>
                                        <li>Réessayez dans quelques minutes</li>
                                        <li>Les données existantes sont préservées</li>
                                    </ul>
                                <?php elseif ($reason === 'not_activated'): ?>
                                    <ul class="mb-0">
                                        <li>Le service n'a pas encore été activé par l'administration</li>
                                        <li>Contactez votre chef de département si nécessaire</li>
                                        <li>Vous serez notifié lors de l'activation</li>
                                        <li>Consultez les annonces pour les mises à jour</li>
                                    </ul>
                                <?php else: ?>
                                    <ul class="mb-0">
                                        <li>Le service est temporairement indisponible</li>
                                        <li>Réessayez dans quelques minutes</li>
                                        <li>Contactez le support si le problème persiste</li>
                                        <li>Vos données sont en sécurité</li>
                                    </ul>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Informations de contact -->
                            <div class="contact-info">
                                <h6><i class="bi bi-telephone text-primary"></i> Besoin d'aide ?</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Chef de Département :</strong><br>
                                        <small>Pour les questions académiques</small>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Support Technique :</strong><br>
                                        <small>Pour les problèmes techniques</small>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Boutons d'action -->
                            <div class="action-buttons">
                                <button onclick="location.reload()" class="btn refresh-button me-3">
                                    <i class="bi bi-arrow-clockwise"></i> Actualiser le Statut
                                </button>
                                
                                <?php if ($userRole === 'enseignant'): ?>
                                    <a href="../enseignant/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Retour au Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'etudiant'): ?>
                                    <a href="../etudiant/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Retour au Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'chef de departement'): ?>
                                    <a href="../chef_departement/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Retour au Tableau de Bord
                                    </a>
                                <?php elseif ($userRole === 'coordinateur'): ?>
                                    <a href="../coordinateur/dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Retour au Tableau de Bord
                                    </a>
                                <?php else: ?>
                                    <a href="../dashboard.php" class="btn btn-outline-primary">
                                        <i class="bi bi-house"></i> Retour au Tableau de Bord
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informations supplémentaires -->
                    <div class="mt-4 text-center text-muted">
                        <small>
                            <i class="bi bi-shield-check"></i>
                            Cette page est sécurisée et vos données sont protégées
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh toutes les 30 secondes pour vérifier si le service est redevenu disponible
        setInterval(function() {
            // Vérifier discrètement le statut du service
            fetch('<?php echo BASE_URL; ?>/route/serviceManagementRoute.php?action=status&service_key=<?php echo urlencode($serviceKey); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.is_active) {
                        // Service redevenu actif, rediriger vers la page originale
                        const originalUrl = sessionStorage.getItem('originalServiceUrl');
                        if (originalUrl) {
                            window.location.href = originalUrl;
                        } else {
                            location.reload();
                        }
                    }
                })
                .catch(error => {
                    console.log('Vérification du statut du service:', error);
                });
        }, 30000);

        // Sauvegarder l'URL originale pour redirection automatique
        if (document.referrer && !sessionStorage.getItem('originalServiceUrl')) {
            sessionStorage.setItem('originalServiceUrl', document.referrer);
        }
    </script>
</body>
</html>
