-- =====================================================
-- Migration vers le Suivi Historique de Charge Horaire
-- =====================================================

-- Étape 1: Créer la table d'historique de charge horaire
-- =====================================================

CREATE TABLE IF NOT EXISTS enseignant_workload_history (
    id_workload INT(11) NOT NULL AUTO_INCREMENT,
    id_enseignant INT(11) NOT NULL,
    annee_universitaire VARCHAR(9) NOT NULL COMMENT 'Format: 2024-2025',
    charge_horaire_accomplie INT(11) NOT NULL DEFAULT 0 COMMENT 'Total des heures accomplies',
    charge_horaire_cours INT(11) NOT NULL DEFAULT 0 COMMENT 'Heures de cours magistraux',
    charge_horaire_td INT(11) NOT NULL DEFAULT 0 COMMENT 'Heures de travaux dirigés',
    charge_horaire_tp INT(11) NOT NULL DEFAULT 0 COMMENT 'Heures de travaux pratiques',
    nombre_modules INT(11) NOT NULL DEFAULT 0 COMMENT 'Nombre de modules assignés',
    derniere_mise_a_jour TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    PRIMARY KEY (id_workload),
    UNIQUE KEY unique_teacher_year (id_enseignant, annee_universitaire),
    FOREIGN KEY (id_enseignant) REFERENCES enseignant(id_enseignant) ON DELETE CASCADE,
    INDEX idx_teacher (id_enseignant),
    INDEX idx_year (annee_universitaire),
    INDEX idx_teacher_year (id_enseignant, annee_universitaire),
    INDEX idx_updated (updated_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
COMMENT='Historique de la charge horaire des enseignants par année universitaire';

-- Étape 2: Fonction pour obtenir l'année universitaire actuelle
-- =====================================================

DELIMITER //

CREATE FUNCTION IF NOT EXISTS getCurrentAcademicYear()
RETURNS VARCHAR(9)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE current_year INT;
    DECLARE academic_year VARCHAR(9);

    SET current_year = YEAR(CURDATE());

    -- Si nous sommes entre septembre et décembre, l'année universitaire commence cette année
    -- Si nous sommes entre janvier et août, l'année universitaire a commencé l'année précédente
    IF MONTH(CURDATE()) >= 9 THEN
        SET academic_year = CONCAT(current_year, '-', current_year + 1);
    ELSE
        SET academic_year = CONCAT(current_year - 1, '-', current_year);
    END IF;

    RETURN academic_year;
END//

DELIMITER ;

-- Étape 3: Migrer les données existantes
-- =====================================================

-- Insérer les données actuelles dans la table d'historique pour l'année en cours
INSERT INTO enseignant_workload_history (
    id_enseignant,
    annee_universitaire,
    charge_horaire_accomplie,
    charge_horaire_cours,
    charge_horaire_td,
    charge_horaire_tp,
    nombre_modules
)
SELECT
    e.id_enseignant,
    getCurrentAcademicYear() as annee_universitaire,
    COALESCE(e.charge_horaire_accomplie, 0) as charge_horaire_accomplie,
    -- Calculer les heures par type depuis les affectations existantes
    COALESCE(cours_hours.total, 0) as charge_horaire_cours,
    COALESCE(td_hours.total, 0) as charge_horaire_td,
    COALESCE(tp_hours.total, 0) as charge_horaire_tp,
    COALESCE(module_count.total, 0) as nombre_modules
FROM enseignant e
LEFT JOIN (
    -- Calculer les heures de cours
    SELECT
        a.id_enseignant,
        SUM(ue.volume_horaire) as total
    FROM affectation a
    JOIN uniteenseignement ue ON a.id_ue = ue.id_ue
    WHERE a.type_enseignement = 'cours'
    GROUP BY a.id_enseignant
) cours_hours ON e.id_enseignant = cours_hours.id_enseignant
LEFT JOIN (
    -- Calculer les heures de TD
    SELECT
        a.id_enseignant,
        SUM(ue.volume_horaire) as total
    FROM affectation a
    JOIN uniteenseignement ue ON a.id_ue = ue.id_ue
    WHERE a.type_enseignement = 'td'
    GROUP BY a.id_enseignant
) td_hours ON e.id_enseignant = td_hours.id_enseignant
LEFT JOIN (
    -- Calculer les heures de TP
    SELECT
        a.id_enseignant,
        SUM(ue.volume_horaire) as total
    FROM affectation a
    JOIN uniteenseignement ue ON a.id_ue = ue.id_ue
    WHERE a.type_enseignement = 'tp'
    GROUP BY a.id_enseignant
) tp_hours ON e.id_enseignant = tp_hours.id_enseignant
LEFT JOIN (
    -- Compter le nombre de modules
    SELECT
        a.id_enseignant,
        COUNT(DISTINCT a.id_ue) as total
    FROM affectation a
    GROUP BY a.id_enseignant
) module_count ON e.id_enseignant = module_count.id_enseignant
ON DUPLICATE KEY UPDATE
    charge_horaire_accomplie = VALUES(charge_horaire_accomplie),
    charge_horaire_cours = VALUES(charge_horaire_cours),
    charge_horaire_td = VALUES(charge_horaire_td),
    charge_horaire_tp = VALUES(charge_horaire_tp),
    nombre_modules = VALUES(nombre_modules),
    updated_at = CURRENT_TIMESTAMP;

-- Étape 4: Créer une vue pour la compatibilité
-- =====================================================

CREATE OR REPLACE VIEW enseignant_current_workload AS
SELECT
    e.id_enseignant,
    e.CNI,
    e.nom,
    e.prenom,
    e.email,
    e.tele,
    e.date_naissance,
    e.lieu_naissance,
    e.sexe,
    e.ville,
    e.pays,
    e.role,
    e.id_specialite,
    e.id_departement,
    e.date_debut_travail,
    e.profile_picture,
    e.charge_horaire_accomplie as legacy_charge_horaire_accomplie,
    COALESCE(ewh.charge_horaire_accomplie, 0) as charge_horaire_accomplie,
    COALESCE(ewh.charge_horaire_cours, 0) as charge_horaire_cours,
    COALESCE(ewh.charge_horaire_td, 0) as charge_horaire_td,
    COALESCE(ewh.charge_horaire_tp, 0) as charge_horaire_tp,
    COALESCE(ewh.nombre_modules, 0) as nombre_modules,
    COALESCE(ewh.annee_universitaire, getCurrentAcademicYear()) as annee_universitaire,
    ewh.derniere_mise_a_jour,
    ewh.created_at as workload_created_at,
    ewh.updated_at as workload_updated_at
FROM enseignant e
LEFT JOIN enseignant_workload_history ewh ON e.id_enseignant = ewh.id_enseignant
    AND ewh.annee_universitaire = getCurrentAcademicYear();

-- Étape 5: Créer des triggers pour maintenir la synchronisation
-- =====================================================

DELIMITER //

-- Trigger pour synchroniser lors de l'insertion dans enseignant_workload_history
CREATE TRIGGER IF NOT EXISTS sync_workload_on_insert
    AFTER INSERT ON enseignant_workload_history
    FOR EACH ROW
BEGIN
    -- Mettre à jour la colonne legacy si c'est l'année courante
    IF NEW.annee_universitaire = getCurrentAcademicYear() THEN
        UPDATE enseignant
        SET charge_horaire_accomplie = NEW.charge_horaire_accomplie
        WHERE id_enseignant = NEW.id_enseignant;
    END IF;
END//

-- Trigger pour synchroniser lors de la mise à jour dans enseignant_workload_history
CREATE TRIGGER IF NOT EXISTS sync_workload_on_update
    AFTER UPDATE ON enseignant_workload_history
    FOR EACH ROW
BEGIN
    -- Mettre à jour la colonne legacy si c'est l'année courante
    IF NEW.annee_universitaire = getCurrentAcademicYear() THEN
        UPDATE enseignant
        SET charge_horaire_accomplie = NEW.charge_horaire_accomplie
        WHERE id_enseignant = NEW.id_enseignant;
    END IF;
END//

DELIMITER ;

-- Étape 6: Créer des procédures stockées utiles
-- =====================================================

DELIMITER //

-- Procédure pour initialiser une nouvelle année universitaire
CREATE PROCEDURE IF NOT EXISTS initializeNewAcademicYear(IN new_year VARCHAR(9))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE teacher_id INT;
    DECLARE teacher_cursor CURSOR FOR SELECT id_enseignant FROM enseignant;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    OPEN teacher_cursor;

    teacher_loop: LOOP
        FETCH teacher_cursor INTO teacher_id;
        IF done THEN
            LEAVE teacher_loop;
        END IF;

        -- Insérer un enregistrement pour chaque enseignant pour la nouvelle année
        INSERT IGNORE INTO enseignant_workload_history (
            id_enseignant,
            annee_universitaire,
            charge_horaire_accomplie,
            charge_horaire_cours,
            charge_horaire_td,
            charge_horaire_tp,
            nombre_modules
        ) VALUES (
            teacher_id,
            new_year,
            0, 0, 0, 0, 0
        );
    END LOOP;

    CLOSE teacher_cursor;
END//

-- Procédure pour recalculer la charge horaire d'un enseignant pour une année
CREATE PROCEDURE IF NOT EXISTS recalculateTeacherWorkload(
    IN teacher_id INT,
    IN academic_year VARCHAR(9)
)
BEGIN
    DECLARE total_hours INT DEFAULT 0;
    DECLARE cours_hours INT DEFAULT 0;
    DECLARE td_hours INT DEFAULT 0;
    DECLARE tp_hours INT DEFAULT 0;
    DECLARE module_count INT DEFAULT 0;

    -- Calculer les heures par type
    SELECT
        COALESCE(SUM(CASE WHEN a.type_enseignement = 'cours' THEN ue.volume_horaire ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN a.type_enseignement = 'td' THEN ue.volume_horaire ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN a.type_enseignement = 'tp' THEN ue.volume_horaire ELSE 0 END), 0),
        COALESCE(COUNT(DISTINCT a.id_ue), 0)
    INTO cours_hours, td_hours, tp_hours, module_count
    FROM affectation a
    JOIN uniteenseignement ue ON a.id_ue = ue.id_ue
    WHERE a.id_enseignant = teacher_id
    AND a.annee_universitaire = academic_year;

    SET total_hours = cours_hours + td_hours + tp_hours;

    -- Mettre à jour ou insérer l'enregistrement
    INSERT INTO enseignant_workload_history (
        id_enseignant,
        annee_universitaire,
        charge_horaire_accomplie,
        charge_horaire_cours,
        charge_horaire_td,
        charge_horaire_tp,
        nombre_modules
    ) VALUES (
        teacher_id,
        academic_year,
        total_hours,
        cours_hours,
        td_hours,
        tp_hours,
        module_count
    ) ON DUPLICATE KEY UPDATE
        charge_horaire_accomplie = total_hours,
        charge_horaire_cours = cours_hours,
        charge_horaire_td = td_hours,
        charge_horaire_tp = tp_hours,
        nombre_modules = module_count,
        updated_at = CURRENT_TIMESTAMP;
END//

DELIMITER ;

-- Étape 7: Vérifications et validation
-- =====================================================

-- Vérifier que la migration s'est bien passée
SELECT
    'Migration Status' as check_type,
    COUNT(*) as total_teachers,
    COUNT(ewh.id_enseignant) as migrated_teachers,
    ROUND((COUNT(ewh.id_enseignant) / COUNT(*)) * 100, 2) as migration_percentage
FROM enseignant e
LEFT JOIN enseignant_workload_history ewh ON e.id_enseignant = ewh.id_enseignant
    AND ewh.annee_universitaire = getCurrentAcademicYear();

-- Afficher un résumé des données migrées
SELECT
    'Data Summary' as info_type,
    annee_universitaire,
    COUNT(*) as teachers_count,
    SUM(charge_horaire_accomplie) as total_hours,
    AVG(charge_horaire_accomplie) as avg_hours_per_teacher,
    SUM(nombre_modules) as total_modules
FROM enseignant_workload_history
GROUP BY annee_universitaire
ORDER BY annee_universitaire DESC;

-- =====================================================
-- Migration terminée avec succès !
-- =====================================================

-- Étape 8: Créer un script PHP pour exécuter la migration
-- =====================================================

-- Ce script SQL doit être exécuté via le script PHP suivant :
-- php docs/execute_migration.php

-- Ou directement via phpMyAdmin/MySQL en important ce fichier

-- =====================================================
-- INSTRUCTIONS POST-MIGRATION
-- =====================================================

-- 1. Vérifier que toutes les tables ont été créées :
SHOW TABLES LIKE '%workload%';

-- 2. Vérifier que les données ont été migrées :
SELECT COUNT(*) as total_records FROM enseignant_workload_history;

-- 3. Vérifier que la vue fonctionne :
SELECT * FROM enseignant_current_workload LIMIT 5;

-- 4. Tester les fonctions :
SELECT getCurrentAcademicYear() as current_year;

-- 5. Vérifier les triggers :
SHOW TRIGGERS LIKE '%workload%';

-- 6. Tester une mise à jour pour vérifier la synchronisation :
-- UPDATE enseignant_workload_history
-- SET charge_horaire_accomplie = charge_horaire_accomplie + 1
-- WHERE id_enseignant = 1 AND annee_universitaire = getCurrentAcademicYear();

-- =====================================================
-- NOTES IMPORTANTES
-- =====================================================

-- ✅ La colonne legacy 'charge_horaire_accomplie' dans la table 'enseignant' est conservée
-- ✅ Les triggers maintiennent la synchronisation automatique
-- ✅ Les fonctions PHP ont été mises à jour pour utiliser le nouveau système
-- ✅ La compatibilité ascendante est assurée
-- ✅ L'historique complet est maintenant disponible

-- ⚠️  PROCHAINES ÉTAPES :
-- 1. Tester l'interface utilisateur : view/chef/workload_history.php
-- 2. Vérifier que les calculs de charge horaire fonctionnent
-- 3. Tester l'export de données
-- 4. Former les utilisateurs sur les nouvelles fonctionnalités

-- =====================================================
