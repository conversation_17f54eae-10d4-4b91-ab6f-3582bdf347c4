-- <PERSON>ript pour créer la table configuration_charge si elle n'existe pas déjà
-- Cette table stocke les configurations de charge de travail minimale pour les enseignants

CREATE TABLE IF NOT EXISTS `configuration_charge` (
  `id_config` int(11) NOT NULL AUTO_INCREMENT,
  `annee_universitaire` varchar(9) COLLATE utf8mb4_general_ci NOT NULL,
  `charge_minimale` int(11) NOT NULL,
  `departement_id` int(11) DEFAULT NULL,
  `type_enseignant` enum('enseignant','vacataire') COLLATE utf8mb4_general_ci DEFAULT 'enseignant',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_config`),
  UNIQUE KEY `unique_annee_dept_type` (`annee_universitaire`, `departement_id`, `type_enseignant`),
  KEY `fk_config_charge_departement` (`departement_id`),
  CONSTRAINT `fk_config_charge_departement` FOREIGN KEY (`departement_id`) REFERENCES `departement` (`id_departement`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Commentaires sur les colonnes :
-- id_config : Identifiant unique de la configuration
-- annee_universitaire : Année universitaire au format YYYY-YYYY (ex: 2024-2025)
-- charge_minimale : Nombre d'heures minimum que doit enseigner un professeur par année
-- departement_id : Référence vers le département (peut être NULL pour une configuration globale)
-- type_enseignant : Type d'enseignant concerné (enseignant permanent ou vacataire)
-- created_at : Date de création de la configuration
-- updated_at : Date de dernière modification

-- Index et contraintes :
-- unique_annee_dept_type : Empêche les doublons pour une même année, département et type d'enseignant
-- fk_config_charge_departement : Clé étrangère vers la table departement

-- Exemples d'insertion :
-- INSERT INTO configuration_charge (annee_universitaire, charge_minimale, departement_id, type_enseignant) 
-- VALUES ('2024-2025', 192, 1, 'enseignant');

-- INSERT INTO configuration_charge (annee_universitaire, charge_minimale, departement_id, type_enseignant) 
-- VALUES ('2024-2025', 96, 1, 'vacataire');
