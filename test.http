###############################################
# Tests pour les tables notifications et messages
###############################################

# Variables globales
@baseUrl = http://localhost/Projet-Web
@notificationsRoute = {{baseUrl}}/route/notificationsRoute.php
@messagesRoute = {{baseUrl}}/route/messagesRoute.php

###############################################
# Tests pour les notifications
###############################################

### Récupérer toutes les notifications
GET {{notificationsRoute}}?action=getAll
Content-Type: application/json

### Créer une nouvelle notification
POST {{notificationsRoute}}?action=createNotification
Content-Type: application/json

{
  "title": "Test de notification",
  "message": "Ceci est un test de création de notification",
  "type": "system",
  "is_read": false
}

### Marquer une notification comme lue
POST {{notificationsRoute}}?action=markAsRead
Content-Type: application/json

{
  "id": 1
}

### Marquer toutes les notifications comme lues
POST {{notificationsRoute}}?action=markAllAsRead
Content-Type: application/json

### Supprimer une notification
POST {{notificationsRoute}}?action=deleteNotification
Content-Type: application/json

{
  "id": 1
}

### Récupérer le nombre de notifications non lues
GET {{notificationsRoute}}?action=getUnreadCount
Content-Type: application/json

###############################################
# Tests pour les messages
###############################################

### Récupérer tous les messages (avec pagination)
GET {{messagesRoute}}?action=getAll&page=1&perPage=10
Content-Type: application/json

### Créer un nouveau message
POST {{messagesRoute}}?action=createMessage
Content-Type: application/json

{
  "sender_id": 1,
  "title": "Test de message",
  "content": "Ceci est un test de création de message",
  "media_url": null,
  "file_path": null
}

### Créer un message administratif
POST {{messagesRoute}}?action=createAdminMessage
Content-Type: application/json

{
  "sender_id": 1,
  "title": "Message administratif",
  "content": "Ceci est un message administratif de test",
  "receiver_type": "student",
  "receiver_id": 1,
  "media_url": null,
  "file_path": null
}

### Marquer un message comme lu
POST {{messagesRoute}}?action=markAsRead
Content-Type: application/json

{
  "id": 1
}

### Marquer tous les messages comme lus
POST {{messagesRoute}}?action=markAllAsRead
Content-Type: application/json

### Supprimer un message
POST {{messagesRoute}}?action=deleteMessage
Content-Type: application/json

{
  "id": 1
}

### Récupérer le nombre de messages non lus
GET {{messagesRoute}}?action=getUnreadCount
Content-Type: application/json

### Vérifier si la base de données a besoin d'être mise à jour
GET {{messagesRoute}}?action=checkIfDatabaseNeedsUpdate
Content-Type: application/json
