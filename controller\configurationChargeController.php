<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to avoid breaking JSON response
ini_set('log_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

require_once __DIR__ . "/../model/configurationChargeModel.php";
require_once __DIR__ . "/../utils/response.php";

/**
 * API endpoint to get workload configurations for a department
 *
 * @param int $departmentId The department ID
 */
function getWorkloadConfigurationsByDepartmentAPI($departmentId) {
    // Validate department ID
    if (!is_numeric($departmentId)) {
        jsonResponse(['error' => 'Invalid department ID'], 400);
    }

    $configurations = getWorkloadConfigurationsByDepartment($departmentId);

    if (isset($configurations['error'])) {
        jsonResponse(['error' => $configurations['error']], 500);
    }

    jsonResponse(['data' => $configurations], 200);
}

/**
 * API endpoint to create a new workload configuration
 */
function createWorkloadConfigurationAPI() {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields (only the fields that exist in the table)
    if (empty($input['annee_universitaire']) || empty($input['charge_minimale'])) {
        jsonResponse(['error' => 'Missing required fields: annee_universitaire, charge_minimale'], 400);
    }

    // Validate charge_minimale is a positive integer
    if (!is_numeric($input['charge_minimale']) || intval($input['charge_minimale']) <= 0) {
        jsonResponse(['error' => 'charge_minimale must be a positive integer'], 400);
    }

    // Validate academic year format (YYYY-YYYY)
    if (!preg_match('/^\d{4}-\d{4}$/', $input['annee_universitaire'])) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
    }

    $result = createWorkloadConfiguration($input);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['message' => $result['success'], 'id' => $result['id']], 201);
}

/**
 * API endpoint to update a workload configuration
 */
function updateWorkloadConfigurationAPI() {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (empty($input['id_config']) || empty($input['annee_universitaire']) || empty($input['charge_minimale'])) {
        jsonResponse(['error' => 'Missing required fields: id_config, annee_universitaire, charge_minimale'], 400);
    }

    // Validate charge_minimale is a positive integer
    if (!is_numeric($input['charge_minimale']) || intval($input['charge_minimale']) <= 0) {
        jsonResponse(['error' => 'charge_minimale must be a positive integer'], 400);
    }

    // Validate academic year format (YYYY-YYYY)
    if (!preg_match('/^\d{4}-\d{4}$/', $input['annee_universitaire'])) {
        jsonResponse(['error' => 'Invalid academic year format. Use YYYY-YYYY'], 400);
    }

    $configId = intval($input['id_config']);
    $result = updateWorkloadConfiguration($configId, $input);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['message' => $result['success']], 200);
}

/**
 * API endpoint to delete a workload configuration
 */
function deleteWorkloadConfigurationAPI() {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || empty($input['id_config'])) {
        jsonResponse(['error' => 'Missing required field: id_config'], 400);
    }

    $configId = intval($input['id_config']);
    $result = deleteWorkloadConfiguration($configId);

    if (isset($result['error'])) {
        jsonResponse(['error' => $result['error']], 400);
    }

    jsonResponse(['message' => $result['success']], 200);
}

/**
 * API endpoint to get a workload configuration by ID
 */
function getWorkloadConfigurationByIdAPI($configId) {
    // Validate config ID
    if (!is_numeric($configId)) {
        jsonResponse(['error' => 'Invalid configuration ID'], 400);
    }

    $configuration = getWorkloadConfigurationById($configId);

    if (isset($configuration['error'])) {
        jsonResponse(['error' => $configuration['error']], 404);
    }

    jsonResponse(['data' => $configuration], 200);
}

/**
 * API endpoint to get current academic year
 */
function getCurrentAcademicYearAPI() {
    $currentYear = getCurrentAcademicYear();
    jsonResponse(['data' => ['academic_year' => $currentYear]], 200);
}

// Handle API requests based on action parameter
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'getWorkloadConfigurationsByDepartment':
            if (!isset($_GET['department_id'])) {
                jsonResponse(['error' => 'Department ID is required'], 400);
            }
            getWorkloadConfigurationsByDepartmentAPI($_GET['department_id']);
            break;

        case 'createWorkloadConfiguration':
            createWorkloadConfigurationAPI();
            break;

        case 'updateWorkloadConfiguration':
            updateWorkloadConfigurationAPI();
            break;

        case 'deleteWorkloadConfiguration':
            deleteWorkloadConfigurationAPI();
            break;

        case 'getWorkloadConfigurationById':
            if (!isset($_GET['config_id'])) {
                jsonResponse(['error' => 'Configuration ID is required'], 400);
            }
            getWorkloadConfigurationByIdAPI($_GET['config_id']);
            break;

        case 'getCurrentAcademicYear':
            getCurrentAcademicYearAPI();
            break;

        default:
            jsonResponse(['error' => 'Invalid action'], 400);
            break;
    }
} else {
    jsonResponse(['error' => 'Action parameter is required'], 400);
}
?>
