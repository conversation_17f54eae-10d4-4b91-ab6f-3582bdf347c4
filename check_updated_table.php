<?php
// Check the updated structure of configuration_charge table
require_once 'config/db.php';

$conn = getConnection();
if (!$conn) {
    die("Connection failed\n");
}

echo "Checking updated configuration_charge table structure...\n\n";

// Show table structure
echo "Table structure:\n";
$result = mysqli_query($conn, "DESCRIBE configuration_charge");
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        echo "  " . $row['Field'] . " - " . $row['Type'] . " - " . $row['Null'] . " - " . $row['Key'] . "\n";
    }
}

echo "\n";

// Show existing data
echo "Existing data:\n";
$result = mysqli_query($conn, "SELECT * FROM configuration_charge LIMIT 10");
if ($result) {
    if (mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            print_r($row);
        }
    } else {
        echo "  No data found.\n";
    }
}

mysqli_close($conn);
?>
