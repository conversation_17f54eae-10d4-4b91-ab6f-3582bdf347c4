<?php
// Déterminer le chemin correct vers le fichier db.php
$dbPath = file_exists(__DIR__ . "/../config/db.php")
    ? __DIR__ . "/../config/db.php"
    : __DIR__ . "/config/db.php";

require_once $dbPath;

/**
 * Récupère un utilisateur par son nom d'utilisateur
 *
 * @param string $username Le nom d'utilisateur
 * @return array Les informations de l'utilisateur ou une erreur
 */
function getUserByUsername($username) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $username = mysqli_real_escape_string($conn, $username);

    $sql = "SELECT * FROM users WHERE username = '$username'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la récupération de l\'utilisateur: ' . mysqli_error($conn)];
    }

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return $user;
    } else {
        mysqli_free_result($result);
        mysqli_close($conn);
        return ['error' => 'Utilisateur non trouvé'];
    }
}

/**
 * Crée un nouvel utilisateur
 *
 * @param string $username Le nom d'utilisateur
 * @param string $password Le mot de passe hashé
 * @param string $role Le rôle de l'utilisateur
 * @return array Résultat de l'opération
 */
function createUser($username, $password, $role) {
    try {
        $conn = getConnection();

        if (!$conn) {
            return ['error' => 'Erreur de connexion à la base de données'];
        }

        // Vérifier si la table users existe
        $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'users'");

        if (mysqli_num_rows($checkTable) == 0) {
            // Créer la table si elle n'existe pas
            $createTable = "CREATE TABLE users (
                id_user INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'enseignant', 'chef de departement', 'coordinateur', 'vacataire') NOT NULL,
                is_active TINYINT(1) DEFAULT 1
            )";

            if (!mysqli_query($conn, $createTable)) {
                mysqli_close($conn);
                return ['error' => 'Erreur lors de la création de la table users: ' . mysqli_error($conn)];
            }

            error_log("Table users créée avec succès");
        }

        // Vérifier si l'utilisateur existe déjà
        $checkUser = mysqli_query($conn, "SELECT * FROM users WHERE username = '$username'");

        if (mysqli_num_rows($checkUser) > 0) {
            mysqli_close($conn);
            return ['error' => 'Un utilisateur avec ce nom d\'utilisateur existe déjà'];
        }

        $username = mysqli_real_escape_string($conn, $username);
        $password = mysqli_real_escape_string($conn, $password);
        $role = mysqli_real_escape_string($conn, $role);

        $sql = "INSERT INTO users (username, password, role) VALUES ('$username', '$password', '$role')";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            $error = mysqli_error($conn);
            mysqli_close($conn);
            return ['error' => 'Erreur lors de la création de l\'utilisateur: ' . $error];
        }

        $userId = mysqli_insert_id($conn);
        mysqli_close($conn);

        return ['success' => true, 'id' => $userId];
    } catch (Exception $e) {
        error_log("Exception dans createUser: " . $e->getMessage());
        return ['error' => 'Exception lors de la création de l\'utilisateur: ' . $e->getMessage()];
    }
}

/**
 * Stocke un token de réinitialisation de mot de passe
 *
 * @param string $username Le nom d'utilisateur
 * @param string $token Le token de réinitialisation
 * @param string $expiry La date d'expiration du token
 * @return array Résultat de l'opération
 */
function storePasswordResetToken($username, $token, $expiry) {
    try {
        $conn = getConnection();

        if (!$conn) {
            return ['error' => 'Erreur de connexion à la base de données'];
        }

        $username = mysqli_real_escape_string($conn, $username);
        $token = mysqli_real_escape_string($conn, $token);
        $expiry = mysqli_real_escape_string($conn, $expiry);

        // Vérifier si la table password_reset_tokens existe
        $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'password_reset_tokens'");

        if (mysqli_num_rows($checkTable) == 0) {
            // Créer la table si elle n'existe pas
            $createTable = "CREATE TABLE password_reset_tokens (
                id INT PRIMARY KEY AUTO_INCREMENT,
                username VARCHAR(50) NOT NULL,
                token VARCHAR(100) NOT NULL,
                expiry DATETIME NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";

            if (!mysqli_query($conn, $createTable)) {
                mysqli_close($conn);
                return ['error' => 'Erreur lors de la création de la table de tokens: ' . mysqli_error($conn)];
            }

            error_log("Table password_reset_tokens créée avec succès");
        }

        // Supprimer les anciens tokens pour cet utilisateur
        $deleteOld = "DELETE FROM password_reset_tokens WHERE username = '$username'";
        mysqli_query($conn, $deleteOld);

        // Insérer le nouveau token
        $sql = "INSERT INTO password_reset_tokens (username, token, expiry) VALUES ('$username', '$token', '$expiry')";
        $result = mysqli_query($conn, $sql);

        if (!$result) {
            $error = mysqli_error($conn);
            mysqli_close($conn);
            return ['error' => 'Erreur lors du stockage du token: ' . $error];
        }

        mysqli_close($conn);
        return ['success' => true];
    } catch (Exception $e) {
        error_log("Exception dans storePasswordResetToken: " . $e->getMessage());
        return ['error' => 'Exception lors du stockage du token: ' . $e->getMessage()];
    }
}

/**
 * Vérifie si un token de réinitialisation est valide
 *
 * @param string $token Le token à vérifier
 * @return array Résultat de la vérification
 */
function verifyPasswordResetToken($token) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $token = mysqli_real_escape_string($conn, $token);
    $now = date('Y-m-d H:i:s');

    $sql = "SELECT * FROM password_reset_tokens WHERE token = '$token' AND expiry > '$now'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la vérification du token: ' . mysqli_error($conn)];
    }

    if (mysqli_num_rows($result) > 0) {
        $tokenData = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return ['success' => true, 'username' => $tokenData['username']];
    } else {
        mysqli_free_result($result);
        mysqli_close($conn);
        return ['error' => 'Token invalide ou expiré'];
    }
}

/**
 * Met à jour le mot de passe d'un utilisateur
 *
 * @param string $username Le nom d'utilisateur
 * @param string $newPassword Le nouveau mot de passe hashé
 * @return array Résultat de l'opération
 */
function updateUserPassword($username, $newPassword) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $username = mysqli_real_escape_string($conn, $username);
    $newPassword = mysqli_real_escape_string($conn, $newPassword);

    $sql = "UPDATE users SET password = '$newPassword' WHERE username = '$username'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la mise à jour du mot de passe: ' . $error];
    }

    // Supprimer les tokens de réinitialisation pour cet utilisateur
    $deleteTokens = "DELETE FROM password_reset_tokens WHERE username = '$username'";
    mysqli_query($conn, $deleteTokens);

    mysqli_close($conn);
    return ['success' => true];
}

/**
 * Met à jour la date de dernière connexion d'un utilisateur
 *
 * @param int $userId L'ID de l'utilisateur
 * @return bool Succès de l'opération
 */
function updateLastLogin($userId) {
    $conn = getConnection();

    if (!$conn) {
        return false;
    }

    // Cette fonction n'est plus utilisée car la table users n'a pas de colonne last_login
    return true;
}

/**
 * Récupère la liste des utilisateurs avec pagination et filtres
 *
 * @param int $page Numéro de page
 * @param string $search Terme de recherche
 * @param string $role Filtre par rôle
 * @param string $status Filtre par statut (actif/inactif)
 * @return array Liste des utilisateurs et informations de pagination
 */
function getUsers($page = 1, $search = '', $role = '', $status = '') {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    // Nombre d'utilisateurs par page
    $perPage = 10;
    $offset = ($page - 1) * $perPage;

    // Construire la requête avec les filtres
    $whereClause = [];
    $params = [];

    if (!empty($search)) {
        $search = mysqli_real_escape_string($conn, $search);
        $whereClause[] = "(username LIKE '%$search%')";
    }

    if (!empty($role)) {
        $role = mysqli_real_escape_string($conn, $role);
        $whereClause[] = "role = '$role'";
    }

    if ($status !== '') {
        $status = (int)$status;
        $whereClause[] = "is_active = $status";
    }

    $where = !empty($whereClause) ? "WHERE " . implode(" AND ", $whereClause) : "";

    // Compter le nombre total d'utilisateurs
    $countSql = "SELECT COUNT(*) as total FROM users $where";
    $countResult = mysqli_query($conn, $countSql);

    if (!$countResult) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors du comptage des utilisateurs: ' . mysqli_error($conn)];
    }

    $totalUsers = mysqli_fetch_assoc($countResult)['total'];
    $totalPages = ceil($totalUsers / $perPage);

    // Récupérer les utilisateurs pour la page actuelle
    $sql = "SELECT id_user, username, role, is_active FROM users $where ORDER BY id_user DESC LIMIT $offset, $perPage";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la récupération des utilisateurs: ' . mysqli_error($conn)];
    }

    $users = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $users[] = $row;
    }

    mysqli_free_result($result);
    mysqli_close($conn);

    return [
        'success' => true,
        'users' => $users,
        'totalUsers' => $totalUsers,
        'totalPages' => $totalPages,
        'currentPage' => $page
    ];
}

/**
 * Récupère les détails d'un utilisateur par son ID
 *
 * @param int $userId L'ID de l'utilisateur
 * @return array Les informations de l'utilisateur ou une erreur
 */
function getUserById($userId) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $userId = mysqli_real_escape_string($conn, $userId);

    $sql = "SELECT id_user, username, role, is_active FROM users WHERE id_user = $userId";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la récupération de l\'utilisateur: ' . mysqli_error($conn)];
    }

    if (mysqli_num_rows($result) > 0) {
        $user = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return ['success' => true, 'user' => $user];
    } else {
        mysqli_free_result($result);
        mysqli_close($conn);
        return ['error' => 'Utilisateur non trouvé'];
    }
}

/**
 * Change le statut (actif/inactif) d'un utilisateur
 *
 * @param int $userId L'ID de l'utilisateur
 * @param int $status Le nouveau statut (1 = actif, 0 = inactif)
 * @return array Résultat de l'opération
 */
function toggleUserStatus($userId, $status) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $userId = mysqli_real_escape_string($conn, $userId);
    $status = (int)$status;

    // Vérifier si l'utilisateur existe
    $checkSql = "SELECT * FROM users WHERE id_user = $userId";
    $checkResult = mysqli_query($conn, $checkSql);

    if (!$checkResult || mysqli_num_rows($checkResult) == 0) {
        mysqli_close($conn);
        return ['error' => 'Utilisateur non trouvé'];
    }

    // Ne pas permettre de désactiver le dernier administrateur actif
    if ($status == 0) {
        $user = mysqli_fetch_assoc($checkResult);
        if ($user['role'] == 'admin') {
            $adminCountSql = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = 1 AND id_user != $userId";
            $adminCountResult = mysqli_query($conn, $adminCountSql);
            $adminCount = mysqli_fetch_assoc($adminCountResult)['count'];

            if ($adminCount == 0) {
                mysqli_close($conn);
                return ['error' => 'Impossible de désactiver le dernier administrateur actif'];
            }
        }
    }

    // Mettre à jour le statut
    $sql = "UPDATE users SET is_active = $status WHERE id_user = $userId";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la mise à jour du statut: ' . mysqli_error($conn)];
    }

    mysqli_close($conn);
    return ['success' => true];
}

/**
 * Supprime un utilisateur
 *
 * @param int $userId L'ID de l'utilisateur
 * @return array Résultat de l'opération
 */
function deleteUser($userId) {
    $conn = getConnection();

    if (!$conn) {
        return ['error' => 'Erreur de connexion à la base de données'];
    }

    $userId = mysqli_real_escape_string($conn, $userId);

    // Vérifier si l'utilisateur existe
    $checkSql = "SELECT * FROM users WHERE id_user = $userId";
    $checkResult = mysqli_query($conn, $checkSql);

    if (!$checkResult || mysqli_num_rows($checkResult) == 0) {
        mysqli_close($conn);
        return ['error' => 'Utilisateur non trouvé'];
    }

    // Ne pas permettre de supprimer le dernier administrateur actif
    $user = mysqli_fetch_assoc($checkResult);
    if ($user['role'] == 'admin' && $user['is_active'] == 1) {
        $adminCountSql = "SELECT COUNT(*) as count FROM users WHERE role = 'admin' AND is_active = 1 AND id_user != $userId";
        $adminCountResult = mysqli_query($conn, $adminCountSql);
        $adminCount = mysqli_fetch_assoc($adminCountResult)['count'];

        if ($adminCount == 0) {
            mysqli_close($conn);
            return ['error' => 'Impossible de supprimer le dernier administrateur actif'];
        }
    }

    // Supprimer l'utilisateur
    $sql = "DELETE FROM users WHERE id_user = $userId";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ['error' => 'Erreur lors de la suppression de l\'utilisateur: ' . mysqli_error($conn)];
    }

    mysqli_close($conn);
    return ['success' => true];
}
