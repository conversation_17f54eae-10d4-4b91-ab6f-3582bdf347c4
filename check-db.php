<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Inclure le fichier de connexion à la base de données
require_once BASE_PATH . "/config/db.php";

// Vérifier la connexion à la base de données
$conn = getConnection();

if (!$conn) {
    die("Erreur de connexion à la base de données");
}

echo "<h1>Vérification de la base de données</h1>";

// Vérifier si la table users existe
$result = mysqli_query($conn, "SHOW TABLES LIKE 'users'");
$usersTableExists = mysqli_num_rows($result) > 0;

echo "<p>Table 'users' existe: " . ($usersTableExists ? "Oui" : "Non") . "</p>";

// Si la table users n'existe pas, la créer
if (!$usersTableExists) {
    echo "<p>Création de la table 'users'...</p>";

    $sql = "CREATE TABLE users (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        role ENUM('admin', 'enseignant', 'chef_de_departement', 'coordinateur', 'vacataire', 'etudiant') NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if (mysqli_query($conn, $sql)) {
        echo "<p>Table 'users' créée avec succès.</p>";
    } else {
        echo "<p>Erreur lors de la création de la table 'users': " . mysqli_error($conn) . "</p>";
    }
}

// Vérifier si la table password_reset_tokens existe
$result = mysqli_query($conn, "SHOW TABLES LIKE 'password_reset_tokens'");
$tokensTableExists = mysqli_num_rows($result) > 0;

echo "<p>Table 'password_reset_tokens' existe: " . ($tokensTableExists ? "Oui" : "Non") . "</p>";

// Si la table password_reset_tokens n'existe pas, la créer
if (!$tokensTableExists) {
    echo "<p>Création de la table 'password_reset_tokens'...</p>";

    $sql = "CREATE TABLE password_reset_tokens (
        id INT PRIMARY KEY AUTO_INCREMENT,
        username VARCHAR(50) NOT NULL,
        token VARCHAR(100) NOT NULL,
        expiry DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";

    if (mysqli_query($conn, $sql)) {
        echo "<p>Table 'password_reset_tokens' créée avec succès.</p>";
    } else {
        echo "<p>Erreur lors de la création de la table 'password_reset_tokens': " . mysqli_error($conn) . "</p>";
    }
}

// Fermer la connexion
mysqli_close($conn);

echo "<p>Vérification terminée.</p>";
echo "<p><a href='generate-test-token.php'>Générer un token de test</a></p>";
?>
