<?php
// Test the API directly
$teacherId = 1; // Test with teacher ID 1

$url = "http://localhost/Projet-Web/route/uePreferencesRoute.php?action=getModulesAndUEsByTeacherSpecialty&teacher_id=$teacherId";

echo "Testing API URL: $url\n\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
if ($error) {
    echo "cURL Error: $error\n";
}
echo "Response: $response\n\n";

// Try to parse JSON
if ($response) {
    $data = json_decode($response, true);
    if ($data) {
        echo "Parsed JSON:\n";
        print_r($data);
    } else {
        echo "Failed to parse JSON. JSON Error: " . json_last_error_msg() . "\n";
    }
}

// Also test with different teacher IDs
echo "\n=== Testing with different teacher IDs ===\n";
for ($id = 1; $id <= 5; $id++) {
    $testUrl = "http://localhost/Projet-Web/route/uePreferencesRoute.php?action=getModulesAndUEsByTeacherSpecialty&teacher_id=$id";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $testUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $testResponse = curl_exec($ch);
    $testHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Teacher ID $id: HTTP $testHttpCode - ";
    if ($testResponse) {
        $testData = json_decode($testResponse, true);
        if (isset($testData['data']) && is_array($testData['data'])) {
            echo count($testData['data']) . " modules found\n";
        } else if (isset($testData['error'])) {
            echo "Error: " . $testData['error'] . "\n";
        } else {
            echo "Unknown response format\n";
        }
    } else {
        echo "No response\n";
    }
}
?>
