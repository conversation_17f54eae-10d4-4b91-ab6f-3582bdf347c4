<?php
/**
 * Script de diagnostic et réparation pour la table pdf_grades
 */

require_once 'config/db.php';

function debugPdfGradesTable() {
    $conn = getConnection();
    if (!$conn) {
        echo "❌ Erreur de connexion à la base de données\n";
        return false;
    }

    echo "🔍 Diagnostic de la table pdf_grades...\n\n";

    // 1. Vérifier si la table existe
    $checkTableQuery = "SHOW TABLES LIKE 'pdf_grades'";
    $tableExists = mysqli_query($conn, $checkTableQuery);

    if (mysqli_num_rows($tableExists) == 0) {
        echo "❌ La table pdf_grades n'existe pas\n";
        echo "🔧 Création de la table...\n";
        createPdfGradesTable($conn);
        return true;
    }

    echo "✅ La table pdf_grades existe\n";

    // 2. Vérifier la structure de la table
    echo "\n📋 Structure actuelle de la table:\n";
    $describeQuery = "DESCRIBE pdf_grades";
    $result = mysqli_query($conn, $describeQuery);

    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            echo "  - {$row['Field']}: {$row['Type']} {$row['Key']} {$row['Extra']}\n";
        }
    }

    // 3. Vérifier les données existantes
    echo "\n📊 Données existantes:\n";
    $countQuery = "SELECT COUNT(*) as count FROM pdf_grades";
    $countResult = mysqli_query($conn, $countQuery);
    if ($countResult) {
        $count = mysqli_fetch_assoc($countResult)['count'];
        echo "  - Nombre d'enregistrements: $count\n";
    }

    // 4. Vérifier les problèmes de clé primaire
    echo "\n🔍 Vérification des problèmes de clé primaire:\n";
    $checkPrimaryKey = "SHOW INDEX FROM pdf_grades WHERE Key_name = 'PRIMARY'";
    $pkResult = mysqli_query($conn, $checkPrimaryKey);

    if ($pkResult && mysqli_num_rows($pkResult) > 0) {
        echo "✅ Clé primaire trouvée\n";

        // Vérifier s'il y a des valeurs nulles ou 0 dans l'ID
        $checkNullIds = "SELECT COUNT(*) as count FROM pdf_grades WHERE id IS NULL OR id = 0";
        $nullResult = mysqli_query($conn, $checkNullIds);
        if ($nullResult) {
            $nullCount = mysqli_fetch_assoc($nullResult)['count'];
            if ($nullCount > 0) {
                echo "❌ Trouvé $nullCount enregistrements avec ID NULL ou 0\n";
                echo "🔧 Correction nécessaire...\n";
                fixPrimaryKeyIssues($conn);
            } else {
                echo "✅ Aucun problème d'ID détecté\n";
            }
        }
    } else {
        echo "❌ Aucune clé primaire trouvée\n";
        echo "🔧 Ajout de la clé primaire...\n";
        addPrimaryKey($conn);
    }

    // 5. Test d'insertion
    echo "\n🧪 Test d'insertion:\n";
    testInsertion($conn);

    mysqli_close($conn);
    return true;
}

function createPdfGradesTable($conn) {
    $createTableQuery = "CREATE TABLE pdf_grades (
        id INT AUTO_INCREMENT PRIMARY KEY,
        id_filiere INT NOT NULL,
        id_enseignant INT NOT NULL,
        id_module INT NOT NULL,
        id_niveau INT NOT NULL,
        id_semestre INT NOT NULL,
        session VARCHAR(50) NOT NULL,
        file_path VARCHAR(255) NOT NULL,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (id_filiere),
        INDEX (id_enseignant),
        INDEX (id_module),
        INDEX (id_niveau)
    )";

    if (mysqli_query($conn, $createTableQuery)) {
        echo "✅ Table pdf_grades créée avec succès\n";
    } else {
        echo "❌ Erreur lors de la création de la table: " . mysqli_error($conn) . "\n";
    }
}

function fixPrimaryKeyIssues($conn) {
    // Supprimer les enregistrements avec ID NULL ou 0
    $deleteQuery = "DELETE FROM pdf_grades WHERE id IS NULL OR id = 0";
    if (mysqli_query($conn, $deleteQuery)) {
        echo "✅ Enregistrements problématiques supprimés\n";
    } else {
        echo "❌ Erreur lors de la suppression: " . mysqli_error($conn) . "\n";
    }

    // Réinitialiser l'AUTO_INCREMENT
    $resetQuery = "ALTER TABLE pdf_grades AUTO_INCREMENT = 1";
    if (mysqli_query($conn, $resetQuery)) {
        echo "✅ AUTO_INCREMENT réinitialisé\n";
    } else {
        echo "❌ Erreur lors de la réinitialisation: " . mysqli_error($conn) . "\n";
    }
}

function addPrimaryKey($conn) {
    // Ajouter une colonne ID si elle n'existe pas
    $addIdQuery = "ALTER TABLE pdf_grades ADD COLUMN id INT AUTO_INCREMENT PRIMARY KEY FIRST";
    if (mysqli_query($conn, $addIdQuery)) {
        echo "✅ Clé primaire ajoutée\n";
    } else {
        echo "❌ Erreur lors de l'ajout de la clé primaire: " . mysqli_error($conn) . "\n";
    }
}

function testInsertion($conn) {
    $testQuery = "INSERT INTO pdf_grades (id_filiere, id_enseignant, id_module, id_niveau, id_semestre, session, file_path)
                  VALUES (1, 1, 1, 1, 1, 'test', 'test_file.pdf')";

    if (mysqli_query($conn, $testQuery)) {
        $insertId = mysqli_insert_id($conn);
        echo "✅ Test d'insertion réussi (ID: $insertId)\n";

        // Supprimer l'enregistrement de test
        $deleteTestQuery = "DELETE FROM pdf_grades WHERE id = $insertId";
        mysqli_query($conn, $deleteTestQuery);
        echo "✅ Enregistrement de test supprimé\n";
    } else {
        echo "❌ Erreur lors du test d'insertion: " . mysqli_error($conn) . "\n";
    }
}

function recreatePdfGradesTable() {
    $conn = getConnection();
    if (!$conn) {
        echo "❌ Erreur de connexion à la base de données\n";
        return false;
    }

    echo "🔧 Recréation complète de la table pdf_grades...\n";

    // Sauvegarder les données existantes
    echo "💾 Sauvegarde des données existantes...\n";
    $backupQuery = "CREATE TABLE pdf_grades_backup AS SELECT * FROM pdf_grades";
    if (mysqli_query($conn, $backupQuery)) {
        echo "✅ Données sauvegardées dans pdf_grades_backup\n";
    } else {
        echo "⚠️ Impossible de sauvegarder (table peut-être vide): " . mysqli_error($conn) . "\n";
    }

    // Supprimer la table existante
    $dropQuery = "DROP TABLE IF EXISTS pdf_grades";
    if (mysqli_query($conn, $dropQuery)) {
        echo "✅ Ancienne table supprimée\n";
    } else {
        echo "❌ Erreur lors de la suppression: " . mysqli_error($conn) . "\n";
        return false;
    }

    // Créer la nouvelle table
    createPdfGradesTable($conn);

    // Restaurer les données si la sauvegarde existe
    $checkBackup = "SHOW TABLES LIKE 'pdf_grades_backup'";
    $backupExists = mysqli_query($conn, $checkBackup);

    if ($backupExists && mysqli_num_rows($backupExists) > 0) {
        echo "🔄 Restauration des données...\n";
        // Vérifier d'abord quelles colonnes existent dans la table de sauvegarde
        $columnsQuery = "SHOW COLUMNS FROM pdf_grades_backup";
        $columnsResult = mysqli_query($conn, $columnsQuery);
        $columns = [];
        while ($row = mysqli_fetch_assoc($columnsResult)) {
            $columns[] = $row['Field'];
        }

        // Construire la requête de restauration en fonction des colonnes disponibles
        $selectColumns = [];
        $insertColumns = [];

        $requiredColumns = ['id_filiere', 'id_enseignant', 'id_module', 'id_niveau', 'id_semestre', 'session', 'file_path'];
        foreach ($requiredColumns as $col) {
            if (in_array($col, $columns)) {
                $selectColumns[] = $col;
                $insertColumns[] = $col;
            }
        }

        if (in_array('upload_date', $columns)) {
            $selectColumns[] = 'upload_date';
            $insertColumns[] = 'upload_date';
        }

        $restoreQuery = "INSERT INTO pdf_grades (" . implode(', ', $insertColumns) . ")
                         SELECT " . implode(', ', $selectColumns) . "
                         FROM pdf_grades_backup WHERE id_filiere IS NOT NULL";

        if (mysqli_query($conn, $restoreQuery)) {
            echo "✅ Données restaurées\n";

            // Supprimer la table de sauvegarde
            $dropBackupQuery = "DROP TABLE pdf_grades_backup";
            mysqli_query($conn, $dropBackupQuery);
            echo "✅ Table de sauvegarde supprimée\n";
        } else {
            echo "❌ Erreur lors de la restauration: " . mysqli_error($conn) . "\n";
        }
    }

    mysqli_close($conn);
    return true;
}

// Interface en ligne de commande
if (php_sapi_name() === 'cli') {
    echo "=== Diagnostic et Réparation de la table pdf_grades ===\n\n";

    if (isset($argv[1]) && $argv[1] === '--recreate') {
        recreatePdfGradesTable();
    } else {
        debugPdfGradesTable();

        echo "\n💡 Pour recréer complètement la table, utilisez:\n";
        echo "   php debug_pdf_grades.php --recreate\n";
    }
} else {
    // Interface web
    echo "<pre>";
    echo "=== Diagnostic et Réparation de la table pdf_grades ===\n\n";

    if (isset($_GET['recreate']) && $_GET['recreate'] === '1') {
        recreatePdfGradesTable();
    } else {
        debugPdfGradesTable();

        echo "\n💡 Pour recréer complètement la table, ajoutez ?recreate=1 à l'URL\n";
    }
    echo "</pre>";
}
?>
