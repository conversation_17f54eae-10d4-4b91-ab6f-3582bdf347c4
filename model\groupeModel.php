<?php
require_once "../config/db.php";

// Fonction pour récupérer la structure de la table groupe
function getGroupeTableStructure() {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getGroupeTableStructure");
        return ["error" => "Database connection error"];
    }

    $sql = "DESCRIBE groupe";
    error_log("Executing SQL: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getGroupeTableStructure: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching table structure: " . $error];
    }

    $structure = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $structure[] = $row;
    }

    error_log("Table structure: " . json_encode($structure));

    mysqli_close($conn);
    return $structure;
}

// Fonction pour récupérer tous les groupes
function getAllGroupes() {
    $conn = getConnection();
    $sql = "SELECT * FROM groupe";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ["error" => "Error fetching groups: " . mysqli_error($conn)];
    }

    $groupes = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $groupes[] = $row;
    }

    mysqli_close($conn);
    return $groupes;
}

// Fonction pour récupérer les groupes par filière et niveau
function getGroupesByFiliereNiveau($id_filiere, $id_niveau) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $id_filiere = mysqli_real_escape_string($conn, $id_filiere);
    $id_niveau = mysqli_real_escape_string($conn, $id_niveau);

    // Vérifier si le niveau appartient au cycle de la filière
    $check_sql = "SELECT COUNT(*) as count FROM niveaux n
                  JOIN filiere f ON n.cycle_id = f.id_cycle
                  WHERE n.id = '$id_niveau' AND f.id_filiere = '$id_filiere'";
    $check_result = mysqli_query($conn, $check_sql);

    if (!$check_result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Error checking niveau-filiere relationship: " . $error];
    }

    $row = mysqli_fetch_assoc($check_result);
    if ($row['count'] == 0) {
        mysqli_close($conn);
        return ["error" => "Le niveau sélectionné ne correspond pas au cycle de la filière choisie"];
    }

    $sql = "SELECT * FROM groupe WHERE id_filiere = '$id_filiere' AND id_niveau = '$id_niveau'";
    $result = mysqli_query($conn, $sql);

    if (!$result) {
        mysqli_close($conn);
        return ["error" => "Error fetching groups: " . mysqli_error($conn)];
    }

    $groupes = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $groupes[] = $row;
    }

    mysqli_close($conn);
    return $groupes;
}

// Fonction pour récupérer les groupes par niveau uniquement
function getGroupesByNiveau($id_niveau) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getGroupesByNiveau");
        return ["error" => "Database connection error"];
    }

    $id_niveau = mysqli_real_escape_string($conn, $id_niveau);

    // Vérifier si le niveau existe
    $check_sql = "SELECT COUNT(*) as count FROM niveaux WHERE id = '$id_niveau'";
    $check_result = mysqli_query($conn, $check_sql);

    if (!$check_result) {
        $error = mysqli_error($conn);
        error_log("Error checking niveau existence: " . $error);
        mysqli_close($conn);
        return ["error" => "Error checking niveau existence: " . $error];
    }

    $row = mysqli_fetch_assoc($check_result);
    if ($row['count'] == 0) {
        error_log("Niveau with ID $id_niveau does not exist");
        mysqli_close($conn);
        return ["error" => "Le niveau sélectionné n'existe pas"];
    }

    $sql = "SELECT * FROM groupe WHERE id_niveau = '$id_niveau'";
    error_log("Executing SQL: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getGroupesByNiveau: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching groups: " . $error];
    }

    $groupes = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $groupes[] = $row;
    }

    error_log("Found " . count($groupes) . " groups for niveau " . $id_niveau);

    mysqli_close($conn);
    return $groupes;
}