<?php
// Debug script to check modules and UEs loading
session_start();
require_once 'config/db.php';

echo "=== DEBUG: Modules and UEs Loading ===\n\n";

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    echo "❌ No user session found\n";
    exit;
}

echo "✅ User session found\n";
echo "User ID: " . ($_SESSION['user']['id'] ?? 'N/A') . "\n";
echo "User role: " . ($_SESSION['user']['role'] ?? 'N/A') . "\n";
echo "User CNI: " . ($_SESSION['user']['username'] ?? 'N/A') . "\n\n";

$conn = getConnection();
if (!$conn) {
    echo "❌ Database connection failed\n";
    exit;
}

echo "✅ Database connection successful\n\n";

// Get teacher info
$username = $_SESSION['user']['username'];
$teacherQuery = "SELECT * FROM enseignant WHERE CNI = '$username'";
$teacherResult = mysqli_query($conn, $teacherQuery);

if (!$teacherResult || mysqli_num_rows($teacherResult) == 0) {
    echo "❌ Teacher not found with CNI: $username\n";
    exit;
}

$teacher = mysqli_fetch_assoc($teacherResult);
$teacherId = $teacher['id'];
$specialtyId = $teacher['id_specialite'];

echo "✅ Teacher found:\n";
echo "  ID: $teacherId\n";
echo "  Name: " . $teacher['nom'] . " " . $teacher['prenom'] . "\n";
echo "  Specialty ID: $specialtyId\n\n";

// Check specialty
$specialtyQuery = "SELECT * FROM specialite WHERE id = '$specialtyId'";
$specialtyResult = mysqli_query($conn, $specialtyQuery);

if (!$specialtyResult || mysqli_num_rows($specialtyResult) == 0) {
    echo "❌ Specialty not found with ID: $specialtyId\n";
    exit;
}

$specialty = mysqli_fetch_assoc($specialtyResult);
echo "✅ Specialty found: " . $specialty['nom'] . "\n\n";

// Check modules for this specialty
$moduleQuery = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
                FROM module m
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN specialite s ON m.specialite_id = s.id
                LEFT JOIN semestre sem ON m.id_semestre = sem.id
                WHERE m.specialite_id = '$specialtyId'
                ORDER BY m.nom";

$moduleResult = mysqli_query($conn, $moduleQuery);

if (!$moduleResult) {
    echo "❌ Error in module query: " . mysqli_error($conn) . "\n";
    exit;
}

$moduleCount = mysqli_num_rows($moduleResult);
echo "📚 Found $moduleCount modules for this specialty:\n\n";

if ($moduleCount == 0) {
    echo "⚠️ No modules found for specialty ID: $specialtyId\n";
    echo "Let's check what modules exist in the database:\n\n";
    
    $allModulesQuery = "SELECT m.id, m.nom, m.specialite_id, s.nom as specialty_name 
                        FROM module m 
                        LEFT JOIN specialite s ON m.specialite_id = s.id 
                        ORDER BY m.nom LIMIT 10";
    $allModulesResult = mysqli_query($conn, $allModulesQuery);
    
    if ($allModulesResult) {
        echo "Sample modules in database:\n";
        while ($row = mysqli_fetch_assoc($allModulesResult)) {
            echo "  - Module: " . $row['nom'] . " (ID: " . $row['id'] . ", Specialty: " . ($row['specialty_name'] ?? 'NULL') . ")\n";
        }
    }
    
    echo "\nSpecialties in database:\n";
    $specialtiesQuery = "SELECT * FROM specialite ORDER BY nom";
    $specialtiesResult = mysqli_query($conn, $specialtiesQuery);
    
    if ($specialtiesResult) {
        while ($row = mysqli_fetch_assoc($specialtiesResult)) {
            echo "  - " . $row['nom'] . " (ID: " . $row['id'] . ")\n";
        }
    }
} else {
    while ($module = mysqli_fetch_assoc($moduleResult)) {
        echo "📖 Module: " . $module['nom'] . "\n";
        echo "   ID: " . $module['id'] . "\n";
        echo "   Filière: " . ($module['nom_filiere'] ?? 'N/A') . "\n";
        echo "   Niveau: " . ($module['niveau'] ?? 'N/A') . "\n";
        echo "   Semestre: " . ($module['semestre'] ?? 'N/A') . "\n";
        
        // Check UEs for this module
        $ueQuery = "SELECT * FROM uniteenseignement WHERE module_id = '" . $module['id'] . "'";
        $ueResult = mysqli_query($conn, $ueQuery);
        
        if ($ueResult) {
            $ueCount = mysqli_num_rows($ueResult);
            echo "   UEs: $ueCount\n";
            
            while ($ue = mysqli_fetch_assoc($ueResult)) {
                echo "     - " . $ue['type'] . " (" . $ue['volume_horaire'] . "h)\n";
            }
        }
        echo "\n";
    }
}

mysqli_close($conn);
echo "\n=== DEBUG COMPLETE ===\n";
?>
