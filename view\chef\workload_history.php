<?php
// Vérifier l'authentification
require_once '../includes/auth_check_chef.php';

// Variables pour la page
$pageTitle = "Historique de Charge Horaire";
$currentPage = "workload_history";
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> | Système de Gestion ENSAH</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">

    <style>
        .year-selector {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            color: white;
        }

        .stats-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .workload-table {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .progress-bar-custom {
            height: 8px;
            border-radius: 4px;
        }

        .teacher-card {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .teacher-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .export-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .filter-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .comparison-view {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <?php include '../includes/sidebar.php'; ?>

        <div class="main-content">
            <!-- Header standard -->
            <?php include '../includes/header.php'; ?>

            <div class="container-fluid p-4">
                <!-- En-tête de page -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">📊 Historique de Charge Horaire</h1>
                        <p class="text-muted">Suivi et analyse de la charge horaire des enseignants</p>
                    </div>
                    <div class="export-buttons">
                        <button type="button" class="btn btn-outline-success" onclick="exportData('csv')">
                            <i class="fas fa-file-csv"></i> Export CSV
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="exportData('json')">
                            <i class="fas fa-file-code"></i> Export JSON
                        </button>
                    </div>
                </div>

                <!-- Sélecteur d'année -->
                <div class="year-selector">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-1">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Année Universitaire
                            </h4>
                            <p class="mb-0 opacity-75">Sélectionnez l'année à consulter</p>
                        </div>
                        <div class="col-md-6">
                            <select class="form-select form-select-lg" id="academicYearSelect" onchange="loadWorkloadData()">
                                <option value="">Chargement...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Statistiques générales -->
                <div class="row" id="statsContainer">
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-primary me-3">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">Total Enseignants</h6>
                                    <h3 class="mb-0" id="totalTeachers">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-success me-3">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">Total Heures</h6>
                                    <h3 class="mb-0" id="totalHours">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-warning me-3">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">Moyenne/Enseignant</h6>
                                    <h3 class="mb-0" id="averageHours">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stats-card">
                            <div class="d-flex align-items-center">
                                <div class="stats-icon bg-info me-3">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted mb-1">Total Modules</h6>
                                    <h3 class="mb-0" id="totalModules">-</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtres -->
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="departmentFilter" class="form-label">Département</label>
                            <select class="form-select" id="departmentFilter" onchange="applyFilters()">
                                <option value="">Tous les départements</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="roleFilter" class="form-label">Rôle</label>
                            <select class="form-select" id="roleFilter" onchange="applyFilters()">
                                <option value="">Tous les rôles</option>
                                <option value="enseignant">Enseignant</option>
                                <option value="chef de departement">Chef de Département</option>
                                <option value="coordinateur">Coordinateur</option>
                                <option value="vacataire">Vacataire</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="searchFilter" class="form-label">Recherche</label>
                            <input type="text" class="form-control" id="searchFilter" placeholder="Nom ou prénom..." onkeyup="applyFilters()">
                        </div>
                    </div>
                </div>

                <!-- Graphiques -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-bar me-2"></i>
                                Répartition de la Charge Horaire
                            </h5>
                            <canvas id="workloadChart" height="300"></canvas>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie me-2"></i>
                                Types d'Enseignement
                            </h5>
                            <canvas id="typeChart" height="300"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Tableau des enseignants -->
                <div class="workload-table">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="workloadTable">
                            <thead class="table-light">
                                <tr>
                                    <th>Enseignant</th>
                                    <th>Département</th>
                                    <th>Rôle</th>
                                    <th>Total Heures</th>
                                    <th>Cours</th>
                                    <th>TD</th>
                                    <th>TP</th>
                                    <th>Modules</th>
                                    <th>Progression</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="workloadTableBody">
                                <tr>
                                    <td colspan="10" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Chargement...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- État vide -->
                <div class="text-center py-5" id="emptyState" style="display: none;">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune donnée disponible</h5>
                    <p class="text-muted">Sélectionnez une année universitaire pour voir les données</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de détail enseignant -->
    <div class="modal fade" id="teacherDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user me-2"></i>
                        Détail de la Charge Horaire
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="teacherDetailContent">
                    <!-- Contenu chargé dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Sidebar JS -->
    <script src="../assets/js/sidebar.js"></script>

    <script>
        let currentData = [];
        let filteredData = [];
        let workloadChart = null;
        let typeChart = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadAvailableYears();
        });

        // Charger les années disponibles
        function loadAvailableYears() {
            fetch('../../controller/workloadHistoryController.php?action=getAvailableYears')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById('academicYearSelect');
                        select.innerHTML = '';
                        
                        data.data.forEach(year => {
                            const option = document.createElement('option');
                            option.value = year;
                            option.textContent = year;
                            if (year === data.current_year) {
                                option.selected = true;
                            }
                            select.appendChild(option);
                        });
                        
                        // Charger les données pour l'année sélectionnée
                        loadWorkloadData();
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur lors du chargement des années', 'error');
                });
        }

        // Charger les données de charge horaire
        function loadWorkloadData() {
            const academicYear = document.getElementById('academicYearSelect').value;
            if (!academicYear) return;

            showLoading(true);

            fetch(`../../controller/workloadHistoryController.php?action=getAllTeachersWorkload&academic_year=${academicYear}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentData = data.data;
                        filteredData = [...currentData];
                        
                        updateStatistics();
                        updateTable();
                        updateCharts();
                        loadDepartments();
                        
                        showLoading(false);
                    } else {
                        throw new Error(data.error || 'Erreur inconnue');
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur lors du chargement des données', 'error');
                    showLoading(false);
                });
        }

        // Mettre à jour les statistiques
        function updateStatistics() {
            const totalTeachers = filteredData.length;
            const totalHours = filteredData.reduce((sum, teacher) => sum + parseInt(teacher.charge_horaire_accomplie || 0), 0);
            const totalModules = filteredData.reduce((sum, teacher) => sum + parseInt(teacher.nombre_modules || 0), 0);
            const averageHours = totalTeachers > 0 ? Math.round(totalHours / totalTeachers) : 0;

            document.getElementById('totalTeachers').textContent = totalTeachers;
            document.getElementById('totalHours').textContent = totalHours + 'h';
            document.getElementById('averageHours').textContent = averageHours + 'h';
            document.getElementById('totalModules').textContent = totalModules;
        }

        // Mettre à jour le tableau
        function updateTable() {
            const tbody = document.getElementById('workloadTableBody');
            
            if (filteredData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="10" class="text-center py-4">
                            <i class="fas fa-search fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Aucun enseignant trouvé</p>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = filteredData.map(teacher => {
                const totalHours = parseInt(teacher.charge_horaire_accomplie || 0);
                const coursHours = parseInt(teacher.charge_horaire_cours || 0);
                const tdHours = parseInt(teacher.charge_horaire_td || 0);
                const tpHours = parseInt(teacher.charge_horaire_tp || 0);
                const modules = parseInt(teacher.nombre_modules || 0);
                
                // Calculer le pourcentage de progression (basé sur 200h comme référence)
                const progressPercent = Math.min((totalHours / 200) * 100, 100);
                const progressColor = progressPercent < 50 ? 'bg-danger' : progressPercent < 80 ? 'bg-warning' : 'bg-success';

                return `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle me-2 d-flex align-items-center justify-content-center">
                                    ${teacher.prenom.charAt(0)}${teacher.nom.charAt(0)}
                                </div>
                                <div>
                                    <strong>${teacher.prenom} ${teacher.nom}</strong>
                                    <br>
                                    <small class="text-muted">${teacher.specialite || 'N/A'}</small>
                                </div>
                            </div>
                        </td>
                        <td>${teacher.departement || 'N/A'}</td>
                        <td>
                            <span class="badge ${getRoleBadgeClass(teacher.role)}">${teacher.role}</span>
                        </td>
                        <td><strong>${totalHours}h</strong></td>
                        <td>${coursHours}h</td>
                        <td>${tdHours}h</td>
                        <td>${tpHours}h</td>
                        <td>${modules}</td>
                        <td>
                            <div class="progress progress-bar-custom">
                                <div class="progress-bar ${progressColor}" style="width: ${progressPercent}%"></div>
                            </div>
                            <small class="text-muted">${Math.round(progressPercent)}%</small>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="showTeacherDetail(${teacher.id_enseignant})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // Obtenir la classe CSS pour le badge de rôle
        function getRoleBadgeClass(role) {
            switch(role) {
                case 'chef de departement': return 'bg-primary';
                case 'coordinateur': return 'bg-info';
                case 'vacataire': return 'bg-warning';
                default: return 'bg-success';
            }
        }

        // Mettre à jour les graphiques
        function updateCharts() {
            updateWorkloadChart();
            updateTypeChart();
        }

        // Graphique de répartition de charge horaire
        function updateWorkloadChart() {
            const ctx = document.getElementById('workloadChart').getContext('2d');
            
            if (workloadChart) {
                workloadChart.destroy();
            }

            const labels = filteredData.slice(0, 10).map(teacher => `${teacher.prenom} ${teacher.nom}`);
            const data = filteredData.slice(0, 10).map(teacher => parseInt(teacher.charge_horaire_accomplie || 0));

            workloadChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Heures d\'enseignement',
                        data: data,
                        backgroundColor: 'rgba(54, 162, 235, 0.8)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Graphique des types d'enseignement
        function updateTypeChart() {
            const ctx = document.getElementById('typeChart').getContext('2d');
            
            if (typeChart) {
                typeChart.destroy();
            }

            const totalCours = filteredData.reduce((sum, teacher) => sum + parseInt(teacher.charge_horaire_cours || 0), 0);
            const totalTD = filteredData.reduce((sum, teacher) => sum + parseInt(teacher.charge_horaire_td || 0), 0);
            const totalTP = filteredData.reduce((sum, teacher) => sum + parseInt(teacher.charge_horaire_tp || 0), 0);

            typeChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Cours', 'TD', 'TP'],
                    datasets: [{
                        data: [totalCours, totalTD, totalTP],
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Charger les départements pour le filtre
        function loadDepartments() {
            const departments = [...new Set(currentData.map(teacher => teacher.departement).filter(Boolean))];
            const select = document.getElementById('departmentFilter');
            
            // Garder l'option "Tous les départements"
            const currentValue = select.value;
            select.innerHTML = '<option value="">Tous les départements</option>';
            
            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept;
                option.textContent = dept;
                select.appendChild(option);
            });
            
            select.value = currentValue;
        }

        // Appliquer les filtres
        function applyFilters() {
            const departmentFilter = document.getElementById('departmentFilter').value;
            const roleFilter = document.getElementById('roleFilter').value;
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();

            filteredData = currentData.filter(teacher => {
                const matchesDepartment = !departmentFilter || teacher.departement === departmentFilter;
                const matchesRole = !roleFilter || teacher.role === roleFilter;
                const matchesSearch = !searchFilter || 
                    teacher.nom.toLowerCase().includes(searchFilter) || 
                    teacher.prenom.toLowerCase().includes(searchFilter);

                return matchesDepartment && matchesRole && matchesSearch;
            });

            updateStatistics();
            updateTable();
            updateCharts();
        }

        // Afficher le détail d'un enseignant
        function showTeacherDetail(teacherId) {
            const academicYear = document.getElementById('academicYearSelect').value;
            
            fetch(`../../controller/workloadHistoryController.php?action=getWorkloadBreakdown&teacher_id=${teacherId}&academic_year=${academicYear}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.data) {
                        const teacher = data.data;
                        const modalContent = document.getElementById('teacherDetailContent');
                        
                        modalContent.innerHTML = `
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Informations Générales</h6>
                                    <p><strong>Nom :</strong> ${teacher.prenom} ${teacher.nom}</p>
                                    <p><strong>Rôle :</strong> ${teacher.role}</p>
                                    <p><strong>Année :</strong> ${teacher.annee_universitaire}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Charge Horaire</h6>
                                    <p><strong>Total :</strong> ${teacher.charge_horaire_accomplie}h</p>
                                    <p><strong>Cours :</strong> ${teacher.charge_horaire_cours}h</p>
                                    <p><strong>TD :</strong> ${teacher.charge_horaire_td}h</p>
                                    <p><strong>TP :</strong> ${teacher.charge_horaire_tp}h</p>
                                    <p><strong>Modules :</strong> ${teacher.nombre_modules}</p>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <button type="button" class="btn btn-primary" onclick="showTeacherHistory(${teacherId})">
                                    <i class="fas fa-history me-2"></i>
                                    Voir l'historique complet
                                </button>
                            </div>
                        `;
                        
                        const modal = new bootstrap.Modal(document.getElementById('teacherDetailModal'));
                        modal.show();
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    showNotification('Erreur lors du chargement du détail', 'error');
                });
        }

        // Exporter les données
        function exportData(format) {
            const academicYear = document.getElementById('academicYearSelect').value;
            if (!academicYear) {
                showNotification('Veuillez sélectionner une année universitaire', 'warning');
                return;
            }

            const url = `../../controller/workloadHistoryController.php?action=exportWorkloadData&academic_year=${academicYear}&format=${format}`;
            
            if (format === 'csv') {
                // Téléchargement direct pour CSV
                window.open(url, '_blank');
            } else {
                // Affichage JSON dans une nouvelle fenêtre
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        const jsonWindow = window.open('', '_blank');
                        jsonWindow.document.write(`
                            <html>
                                <head><title>Export JSON - ${academicYear}</title></head>
                                <body>
                                    <pre>${JSON.stringify(data, null, 2)}</pre>
                                </body>
                            </html>
                        `);
                    })
                    .catch(error => {
                        console.error('Erreur:', error);
                        showNotification('Erreur lors de l\'export', 'error');
                    });
            }
        }

        // Afficher/masquer le chargement
        function showLoading(show) {
            const table = document.getElementById('workloadTable');
            const emptyState = document.getElementById('emptyState');
            
            if (show) {
                table.style.display = 'none';
                emptyState.style.display = 'none';
            } else {
                table.style.display = 'block';
                emptyState.style.display = filteredData.length === 0 ? 'block' : 'none';
            }
        }

        // Afficher une notification
        function showNotification(message, type = 'info') {
            // Implémentation simple de notification
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'warning' ? 'alert-warning' : 
                              type === 'success' ? 'alert-success' : 'alert-info';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto-suppression après 5 secondes
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }
    </script>
</body>
</html>
