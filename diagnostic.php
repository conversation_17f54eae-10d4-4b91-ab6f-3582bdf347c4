<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Inclure le fichier de connexion à la base de données
require_once BASE_PATH . "/config/db.php";

// Fonction pour vérifier la structure d'une table
function checkTableStructure($tableName) {
    $conn = getConnection();
    
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }
    
    // Vérifier si la table existe
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    
    if (mysqli_num_rows($checkTable) == 0) {
        mysqli_close($conn);
        return ["error" => "La table '$tableName' n'existe pas"];
    }
    
    // Récupérer la structure de la table
    $result = mysqli_query($conn, "DESCRIBE $tableName");
    
    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération de la structure de la table: $error"];
    }
    
    $columns = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $columns[] = $row;
    }
    
    mysqli_close($conn);
    return ["success" => true, "columns" => $columns];
}

// Fonction pour vérifier les données d'une table
function checkTableData($tableName, $limit = 10) {
    $conn = getConnection();
    
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }
    
    // Vérifier si la table existe
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE '$tableName'");
    
    if (mysqli_num_rows($checkTable) == 0) {
        mysqli_close($conn);
        return ["error" => "La table '$tableName' n'existe pas"];
    }
    
    // Récupérer les données de la table
    $result = mysqli_query($conn, "SELECT * FROM $tableName LIMIT $limit");
    
    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération des données de la table: $error"];
    }
    
    $data = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }
    
    mysqli_close($conn);
    return ["success" => true, "data" => $data];
}

// Fonction pour vérifier les informations d'un utilisateur
function checkUserInfo($username) {
    $conn = getConnection();
    
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }
    
    // Échapper les caractères spéciaux
    $username = mysqli_real_escape_string($conn, $username);
    
    // Vérifier dans la table users
    $result = mysqli_query($conn, "SELECT * FROM users WHERE username = '$username'");
    
    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération des informations de l'utilisateur: $error"];
    }
    
    $userData = [];
    if (mysqli_num_rows($result) > 0) {
        $userData['users'] = mysqli_fetch_assoc($result);
    }
    
    // Vérifier dans la table enseignant
    $result = mysqli_query($conn, "SELECT * FROM enseignant WHERE CNI = '$username'");
    
    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération des informations de l'enseignant: $error"];
    }
    
    if (mysqli_num_rows($result) > 0) {
        $userData['enseignant'] = mysqli_fetch_assoc($result);
        
        // Si l'utilisateur est un enseignant, vérifier s'il est coordinateur d'une filière
        if (isset($userData['enseignant']['id_enseignant'])) {
            $enseignantId = $userData['enseignant']['id_enseignant'];
            
            // Vérifier dans la table filiere
            $result = mysqli_query($conn, "SELECT * FROM filiere WHERE id_chef_filiere = '$enseignantId'");
            
            if ($result && mysqli_num_rows($result) > 0) {
                $userData['filiere'] = mysqli_fetch_assoc($result);
            } else {
                // Essayer avec d'autres noms de colonnes possibles
                $possibleColumns = ['chef_filiere', 'id_coordinateur', 'coordinateur_id'];
                
                foreach ($possibleColumns as $column) {
                    $checkColumn = mysqli_query($conn, "SHOW COLUMNS FROM filiere LIKE '$column'");
                    
                    if ($checkColumn && mysqli_num_rows($checkColumn) > 0) {
                        $result = mysqli_query($conn, "SELECT * FROM filiere WHERE $column = '$enseignantId'");
                        
                        if ($result && mysqli_num_rows($result) > 0) {
                            $userData['filiere'] = mysqli_fetch_assoc($result);
                            $userData['filiere_column'] = $column;
                            break;
                        }
                    }
                }
            }
        }
    }
    
    mysqli_close($conn);
    return ["success" => true, "data" => $userData];
}

// Fonction pour associer un enseignant à une filière
function associateCoordinatorToFiliere($enseignantId, $filiereId) {
    $conn = getConnection();
    
    if (!$conn) {
        return ["error" => "Erreur de connexion à la base de données"];
    }
    
    // Échapper les caractères spéciaux
    $enseignantId = mysqli_real_escape_string($conn, $enseignantId);
    $filiereId = mysqli_real_escape_string($conn, $filiereId);
    
    // Vérifier la structure de la table filiere
    $result = mysqli_query($conn, "DESCRIBE filiere");
    
    if (!$result) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de la récupération de la structure de la table filiere: $error"];
    }
    
    $chefFiliereColumn = 'id_chef_filiere'; // Colonne par défaut
    
    while ($row = mysqli_fetch_assoc($result)) {
        if (in_array($row['Field'], ['id_chef_filiere', 'chef_filiere', 'id_coordinateur', 'coordinateur_id'])) {
            $chefFiliereColumn = $row['Field'];
            break;
        }
    }
    
    // Mettre à jour la filière
    $updateResult = mysqli_query($conn, "UPDATE filiere SET $chefFiliereColumn = '$enseignantId' WHERE id_filiere = '$filiereId'");
    
    if (!$updateResult) {
        $error = mysqli_error($conn);
        mysqli_close($conn);
        return ["error" => "Erreur lors de l'association du coordinateur à la filière: $error"];
    }
    
    mysqli_close($conn);
    return ["success" => true, "message" => "Coordinateur associé à la filière avec succès"];
}

// Afficher les résultats
echo "<h1>Diagnostic du système</h1>";

// Vérifier la structure de la table filiere
$filiereStructure = checkTableStructure('filiere');
echo "<h2>Structure de la table filiere</h2>";

if (isset($filiereStructure['error'])) {
    echo "<p>Erreur: " . $filiereStructure['error'] . "</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($filiereStructure['columns'] as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Vérifier les données de la table filiere
$filiereData = checkTableData('filiere');
echo "<h2>Données de la table filiere</h2>";

if (isset($filiereData['error'])) {
    echo "<p>Erreur: " . $filiereData['error'] . "</p>";
} else {
    if (count($filiereData['data']) > 0) {
        echo "<table border='1'>";
        
        // En-têtes de colonnes
        echo "<tr>";
        foreach (array_keys($filiereData['data'][0]) as $header) {
            echo "<th>" . $header . "</th>";
        }
        echo "</tr>";
        
        // Données
        foreach ($filiereData['data'] as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Aucune donnée trouvée dans la table filiere</p>";
    }
}

// Vérifier les informations de l'utilisateur salmaCor
$userInfo = checkUserInfo('salmaCor');
echo "<h2>Informations de l'utilisateur salmaCor</h2>";

if (isset($userInfo['error'])) {
    echo "<p>Erreur: " . $userInfo['error'] . "</p>";
} else {
    if (isset($userInfo['data']['users'])) {
        echo "<h3>Données de la table users</h3>";
        echo "<pre>" . print_r($userInfo['data']['users'], true) . "</pre>";
    } else {
        echo "<p>Aucune donnée trouvée dans la table users pour cet utilisateur</p>";
    }
    
    if (isset($userInfo['data']['enseignant'])) {
        echo "<h3>Données de la table enseignant</h3>";
        echo "<pre>" . print_r($userInfo['data']['enseignant'], true) . "</pre>";
    } else {
        echo "<p>Aucune donnée trouvée dans la table enseignant pour cet utilisateur</p>";
    }
    
    if (isset($userInfo['data']['filiere'])) {
        echo "<h3>Données de la table filiere</h3>";
        echo "<pre>" . print_r($userInfo['data']['filiere'], true) . "</pre>";
        
        if (isset($userInfo['data']['filiere_column'])) {
            echo "<p>Colonne utilisée pour l'association: " . $userInfo['data']['filiere_column'] . "</p>";
        }
    } else {
        echo "<p>Aucune filière associée à cet utilisateur</p>";
        
        // Formulaire pour associer l'utilisateur à une filière
        if (isset($userInfo['data']['enseignant']['id_enseignant'])) {
            echo "<h3>Associer l'utilisateur à une filière</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='enseignant_id' value='" . $userInfo['data']['enseignant']['id_enseignant'] . "'>";
            echo "<label for='filiere_id'>ID de la filière:</label>";
            echo "<input type='text' name='filiere_id' id='filiere_id' required>";
            echo "<button type='submit' name='associate'>Associer</button>";
            echo "</form>";
        }
    }
}

// Traiter le formulaire d'association
if (isset($_POST['associate'])) {
    $enseignantId = $_POST['enseignant_id'];
    $filiereId = $_POST['filiere_id'];
    
    $associationResult = associateCoordinatorToFiliere($enseignantId, $filiereId);
    
    if (isset($associationResult['error'])) {
        echo "<p>Erreur: " . $associationResult['error'] . "</p>";
    } else {
        echo "<p>" . $associationResult['message'] . "</p>";
        echo "<p>Veuillez <a href='diagnostic.php'>actualiser la page</a> pour voir les changements.</p>";
    }
}
?>
